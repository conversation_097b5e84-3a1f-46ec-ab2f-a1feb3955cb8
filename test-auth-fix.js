#!/usr/bin/env node

/**
 * Test Authentication Fix
 * Quick test to verify authentication improvements
 */

const { chromium } = require('playwright');

async function testAuthFix() {
  console.log('🧪 Testing Authentication Fix...\n');
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    console.log('1️⃣ Navigating to homepage...');
    await page.goto('http://localhost:3001');
    await page.waitForLoadState('networkidle');
    
    // Check initial state
    const loginBtn = await page.locator('[aria-label="Log in to your account"]').first();
    const loginVisible = await loginBtn.isVisible().catch(() => false);
    console.log(`   Login button visible: ${loginVisible} ✅`);

    console.log('\n2️⃣ Logging in...');
    await page.goto('http://localhost:3001/login');
    await page.waitForLoadState('networkidle');
    
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'testpassword');
    await page.click('button[type="submit"]');
    
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    console.log(`   Current URL: ${page.url()}`);

    console.log('\n3️⃣ Checking authenticated state...');
    
    // Check for logout button with improved selectors
    const logoutSelectors = [
      'button[aria-label="Sign out of your account"]',
      'button:has-text("Sign Out")',
      'button:has-text("Sign out")'
    ];
    
    let logoutFound = false;
    for (const selector of logoutSelectors) {
      const element = await page.locator(selector).first();
      if (await element.isVisible().catch(() => false)) {
        console.log(`   ✅ Found logout button: ${selector}`);
        logoutFound = true;
        break;
      }
    }
    
    if (!logoutFound) {
      console.log('   ❌ No logout button found');
    }

    // Check for user-specific content
    const dashboardLink = await page.locator('a[href="/dashboard"]').first();
    const dashboardVisible = await dashboardLink.isVisible().catch(() => false);
    console.log(`   Dashboard link visible: ${dashboardVisible} ${dashboardVisible ? '✅' : '❌'}`);

    // Check for profile link with user info
    const profileLink = await page.locator('a[href="/profile"]').first();
    const profileVisible = await profileLink.isVisible().catch(() => false);
    console.log(`   Profile link visible: ${profileVisible} ${profileVisible ? '✅' : '❌'}`);

    console.log('\n4️⃣ Testing dashboard access...');
    await page.goto('http://localhost:3001/dashboard');
    await page.waitForLoadState('networkidle');
    
    const finalUrl = page.url();
    console.log(`   Final URL: ${finalUrl}`);
    
    if (finalUrl.includes('/dashboard')) {
      console.log('   ✅ Dashboard accessible');
    } else if (finalUrl.includes('/login')) {
      console.log('   ❌ Redirected to login');
    } else {
      console.log('   ❓ Unexpected redirect');
    }

    console.log('\n5️⃣ Testing session persistence...');
    await page.reload();
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    const afterReloadUrl = page.url();
    console.log(`   URL after reload: ${afterReloadUrl}`);
    
    // Check if still authenticated after reload
    const logoutAfterReload = await page.locator('button[aria-label="Sign out of your account"]').first();
    const logoutVisibleAfterReload = await logoutAfterReload.isVisible().catch(() => false);
    console.log(`   Logout button after reload: ${logoutVisibleAfterReload} ${logoutVisibleAfterReload ? '✅' : '❌'}`);

    console.log('\n📊 SUMMARY:');
    console.log(`   Login process: ${!page.url().includes('/login') ? '✅ Working' : '❌ Failed'}`);
    console.log(`   Authentication UI: ${logoutFound ? '✅ Working' : '❌ Failed'}`);
    console.log(`   Dashboard access: ${finalUrl.includes('/dashboard') ? '✅ Working' : '❌ Failed'}`);
    console.log(`   Session persistence: ${logoutVisibleAfterReload ? '✅ Working' : '❌ Failed'}`);

    // Wait for manual inspection
    console.log('\n⏸️ Pausing for manual inspection...');
    await page.waitForTimeout(10000);

  } catch (error) {
    console.error('❌ Test error:', error);
  } finally {
    await browser.close();
  }
}

testAuthFix().catch(console.error);

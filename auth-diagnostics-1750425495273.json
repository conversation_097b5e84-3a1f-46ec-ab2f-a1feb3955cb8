{"timestamp": "2025-06-20T13:17:58.271Z", "tests": [{"testName": "HOMEPAGE_ACCESS", "status": "PASSED", "details": "Homepage loads successfully", "severity": "LOW", "timestamp": "2025-06-20T13:18:03.128Z"}, {"testName": "UNAUTHENTICATED_STATE", "status": "PASSED", "details": "Shows login/signup, hides authenticated elements", "severity": "LOW", "timestamp": "2025-06-20T13:18:03.201Z"}, {"testName": "LOGIN_FLOW", "status": "FAILED", "details": "Response: 401, URL: http://localhost:3001/login", "severity": "CRITICAL", "timestamp": "2025-06-20T13:18:11.231Z"}, {"testName": "AUTHENTICATED_STATE", "status": "FAILED", "details": "Authentication state inconsistent", "severity": "HIGH", "timestamp": "2025-06-20T13:18:12.176Z"}, {"testName": "PROTECTED_ROUTE_ACCESS", "status": "FAILED", "details": "Authenticated user redirected to login", "severity": "HIGH", "timestamp": "2025-06-20T13:18:13.844Z"}, {"testName": "SESSION_PERSISTENCE", "status": "FAILED", "details": "Session lost after page reload", "severity": "HIGH", "timestamp": "2025-06-20T13:18:14.779Z"}, {"testName": "LOGOUT_FLOW", "status": "FAILED", "details": "Logout button not accessible", "severity": "MEDIUM", "timestamp": "2025-06-20T13:18:14.780Z"}, {"testName": "API_AUTHENTICATION", "status": "PASSED", "details": "API accepts authenticated requests", "severity": "LOW", "timestamp": "2025-06-20T13:18:15.140Z"}], "summary": {"total": 8, "passed": 3, "failed": 5, "critical": 1}}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>testerat Enhanced Report - Enhanced Testerat - http://localhost:3001</title>
    <style>
        
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .report-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; }
        .report-header h1 { font-size: 2.5em; margin-bottom: 10px; }
        .report-meta p { margin: 5px 0; opacity: 0.9; }
        .summary-section { background: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-top: 20px; }
        .summary-card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #007bff; }
        .summary-card.success { border-left-color: #28a745; }
        .summary-card.warning { border-left-color: #ffc107; }
        .summary-card.critical { border-left-color: #dc3545; }
        .status-indicator { font-size: 1.2em; font-weight: bold; margin-top: 10px; }
        .status-indicator.success { color: #28a745; }
        .status-indicator.warning { color: #ffc107; }
        .status-indicator.critical { color: #dc3545; }
        .test-stats { display: flex; justify-content: space-between; margin-top: 10px; }
        .stat { text-align: center; }
        .stat-number { display: block; font-size: 2em; font-weight: bold; }
        .stat-number.passed { color: #28a745; }
        .stat-number.failed { color: #dc3545; }
        .stat-number.critical { color: #dc3545; font-weight: 900; }
        .stat-label { font-size: 0.9em; color: #666; }
        .progress-bar { background: #e9ecef; height: 20px; border-radius: 10px; overflow: hidden; margin: 10px 0; }
        .progress-fill { background: linear-gradient(90deg, #28a745, #20c997); height: 100%; transition: width 0.3s ease; }
        .percentage { font-size: 1.2em; font-weight: bold; color: #28a745; }
        .execution-time .time-value { font-size: 1.5em; font-weight: bold; color: #007bff; }
        .critical-issues-section { background: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .critical-issue { background: #fff5f5; border: 1px solid #fed7d7; border-radius: 8px; padding: 20px; margin-bottom: 20px; }
        .issue-header { display: flex; justify-content: between; align-items: center; margin-bottom: 15px; }
        .severity-badge { padding: 4px 12px; border-radius: 20px; font-size: 0.8em; font-weight: bold; text-transform: uppercase; }
        .severity-badge.critical { background: #dc3545; color: white; }
        .severity-badge.high { background: #fd7e14; color: white; }
        .severity-badge.medium { background: #ffc107; color: #212529; }
        .severity-badge.low { background: #6c757d; color: white; }
        .issue-details { margin-bottom: 15px; }
        .issue-recommendations ul { margin-left: 20px; }
        .fix-examples { margin-top: 15px; }
        .fix-example { background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 5px 0; }
        .fix-example code { font-family: 'Monaco', 'Menlo', monospace; }
        .charts-section { background: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .charts-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 30px; margin-top: 20px; }
        .chart-container { text-align: center; }
        .detailed-results-section { background: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .category-section { margin-bottom: 30px; }
        .results-table table { width: 100%; border-collapse: collapse; margin-top: 15px; }
        .results-table th, .results-table td { padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6; }
        .results-table th { background: #f8f9fa; font-weight: 600; }
        .result-row.failed { background: #fff5f5; }
        .result-row.passed { background: #f0fff4; }
        .status-badge { padding: 4px 8px; border-radius: 4px; font-size: 0.8em; font-weight: bold; }
        .status-badge.passed { background: #d4edda; color: #155724; }
        .status-badge.failed { background: #f8d7da; color: #721c24; }
        .status-badge.critical { background: #dc3545; color: white; }
        .report-footer { text-align: center; margin-top: 50px; padding: 20px; color: #666; border-top: 1px solid #dee2e6; }
        
    </style>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container">
        <header class="report-header">
            <h1>🎯 testerat Enhanced Report</h1>
            <div class="report-meta">
                <p><strong>Test Suite:</strong> Enhanced Testerat - http://localhost:3001</p>
                <p><strong>Generated:</strong> 2025-06-20 14:58:31</p>
                <p><strong>Framework:</strong> FrameworkType.NEXTJS</p>
                <p><strong>Application:</strong> FAAFO Career Platform - Find Your Path to Career Freedom</p>
            </div>
        </header>
        
        
        <section class="summary-section">
            <h2>📊 Test Summary</h2>
            <div class="summary-grid">
                <div class="summary-card warning">
                    <h3>Overall Status</h3>
                    <div class="status-indicator warning">
                        ⚠️ ISSUES FOUND
                    </div>
                </div>
                <div class="summary-card">
                    <h3>Test Results</h3>
                    <div class="test-stats">
                        <div class="stat">
                            <span class="stat-number">12</span>
                            <span class="stat-label">Total Tests</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number passed">4</span>
                            <span class="stat-label">Passed</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number failed">7</span>
                            <span class="stat-label">Failed</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number critical">0</span>
                            <span class="stat-label">Critical</span>
                        </div>
                    </div>
                </div>
                <div class="summary-card">
                    <h3>Success Rate</h3>
                    <div class="success-rate">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 33.3%"></div>
                        </div>
                        <span class="percentage">33.3%</span>
                    </div>
                </div>
                <div class="summary-card">
                    <h3>Execution Time</h3>
                    <div class="execution-time">
                        <span class="time-value">13.00s</span>
                    </div>
                </div>
            </div>
        </section>
        
        
            <section class="critical-issues-section">
                <h2>🎉 No Critical Issues Found</h2>
                <div class="no-issues">
                    <p>Excellent! No critical issues were detected in your application.</p>
                </div>
            </section>
            
        
        <section class="charts-section">
            <h2>📈 Visual Analysis</h2>
            <div class="charts-grid">
                <div class="chart-container">
                    <h3>Test Results Distribution</h3>
                    <canvas id="resultsChart" width="400" height="200"></canvas>
                </div>
                <div class="chart-container">
                    <h3>Severity Breakdown</h3>
                    <canvas id="severityChart" width="400" height="200"></canvas>
                </div>
                <div class="chart-container">
                    <h3>Category Performance</h3>
                    <canvas id="categoryChart" width="400" height="200"></canvas>
                </div>
            </div>
        </section>
        
        
        <section class="fix-recommendations-section">
            <h2>🔧 Fix Recommendations</h2>
            
            <div class="recommendations-group critical">
                <h3>🚨 Critical Fixes Needed</h3>
                <ul>
            <li>Fix access control for /dashboard</li><li>Fix API error: 401</li><li>Fix form submission issues in form 1</li></ul></div>
            <div class="recommendations-group general">
                <h3>💡 General Improvements</h3>
                <ul>
            <li>Ensure authenticated users see personalized content</li><li>Implement proper session persistence</li><li>Implement CSRF protection</li><li>Encrypt or remove sensitive data from API requests</li><li>Ensure authentication state is properly checked before rendering content</li><li>Add workflow testing if multi-step processes exist</li><li>Ensure logout functionality is accessible</li></ul></div>
        </section>
        
        
        <section class="detailed-results-section">
            <h2>📋 Detailed Test Results</h2>
            
            <div class="category-section">
                <h3>General Tests (5)</h3>
                <div class="results-table">
                    <table>
                        <thead>
                            <tr>
                                <th>Test Name</th>
                                <th>Status</th>
                                <th>Severity</th>
                                <th>Details</th>
                                <th>Execution Time</th>
                            </tr>
                        </thead>
                        <tbody>
                            
        <tr class="result-row failed">
            <td class="test-name">Auth State Consistency</td>
            <td class="status">
                <span class="status-badge failed">FAILED</span>
            </td>
            <td class="severity">
                <span class="severity-badge high">HIGH</span>
            </td>
            <td class="details">Logged-out content visible to authenticated user; No user-specific content found for authenticated user</td>
            <td class="execution-time">0.00s</td>
        </tr>
        
        <tr class="result-row failed">
            <td class="test-name">Protected Route Access</td>
            <td class="status">
                <span class="status-badge failed">FAILED</span>
            </td>
            <td class="severity">
                <span class="severity-badge high">HIGH</span>
            </td>
            <td class="details">Tested 1 protected routes; Authenticated user redirected from protected route: /dashboard</td>
            <td class="execution-time">0.00s</td>
        </tr>
        
        <tr class="result-row failed">
            <td class="test-name">Session Management</td>
            <td class="status">
                <span class="status-badge failed">FAILED</span>
            </td>
            <td class="severity">
                <span class="severity-badge high">HIGH</span>
            </td>
            <td class="details">Session not persisted across page reload; CSRF token not found</td>
            <td class="execution-time">0.00s</td>
        </tr>
        
        <tr class="result-row failed">
            <td class="test-name">Logout Flow</td>
            <td class="status">
                <span class="status-badge failed">FAILED</span>
            </td>
            <td class="severity">
                <span class="severity-badge medium">MEDIUM</span>
            </td>
            <td class="details">Logout button not found</td>
            <td class="execution-time">0.00s</td>
        </tr>
        
        <tr class="result-row passed">
            <td class="test-name">Auth Edge Cases</td>
            <td class="status">
                <span class="status-badge passed">PASSED</span>
            </td>
            <td class="severity">
                <span class="severity-badge low">LOW</span>
            </td>
            <td class="details">Authentication edge cases handled properly</td>
            <td class="execution-time">0.00s</td>
        </tr>
        
                        </tbody>
                    </table>
                </div>
            </div>
            
            <div class="category-section">
                <h3>Workflow Tests (1)</h3>
                <div class="results-table">
                    <table>
                        <thead>
                            <tr>
                                <th>Test Name</th>
                                <th>Status</th>
                                <th>Severity</th>
                                <th>Details</th>
                                <th>Execution Time</th>
                            </tr>
                        </thead>
                        <tbody>
                            
        <tr class="result-row skipped">
            <td class="test-name">Workflow Detection</td>
            <td class="status">
                <span class="status-badge skipped">SKIPPED</span>
            </td>
            <td class="severity">
                <span class="severity-badge low">LOW</span>
            </td>
            <td class="details">No multi-step workflow detected</td>
            <td class="execution-time">0.00s</td>
        </tr>
        
                        </tbody>
                    </table>
                </div>
            </div>
            
            <div class="category-section">
                <h3>Api Tests (6)</h3>
                <div class="results-table">
                    <table>
                        <thead>
                            <tr>
                                <th>Test Name</th>
                                <th>Status</th>
                                <th>Severity</th>
                                <th>Details</th>
                                <th>Execution Time</th>
                            </tr>
                        </thead>
                        <tbody>
                            
        <tr class="result-row passed">
            <td class="test-name">Csrf Protection</td>
            <td class="status">
                <span class="status-badge passed">PASSED</span>
            </td>
            <td class="severity">
                <span class="severity-badge low">LOW</span>
            </td>
            <td class="details">CSRF protection working properly</td>
            <td class="execution-time">0.00s</td>
        </tr>
        
        <tr class="result-row failed">
            <td class="test-name">Form Submissions</td>
            <td class="status">
                <span class="status-badge failed">FAILED</span>
            </td>
            <td class="severity">
                <span class="severity-badge high">HIGH</span>
            </td>
            <td class="details">Form 1 submission not captured</td>
            <td class="execution-time">0.00s</td>
        </tr>
        
        <tr class="result-row passed">
            <td class="test-name">Api Endpoints</td>
            <td class="status">
                <span class="status-badge passed">PASSED</span>
            </td>
            <td class="severity">
                <span class="severity-badge low">LOW</span>
            </td>
            <td class="details">API endpoints working properly</td>
            <td class="execution-time">0.00s</td>
        </tr>
        
        <tr class="result-row failed">
            <td class="test-name">Network Requests</td>
            <td class="status">
                <span class="status-badge failed">FAILED</span>
            </td>
            <td class="severity">
                <span class="severity-badge medium">MEDIUM</span>
            </td>
            <td class="details">Failed request: POST http://localhost:3001/api/auth/callback/credentials - 401</td>
            <td class="execution-time">0.00s</td>
        </tr>
        
        <tr class="result-row passed">
            <td class="test-name">Api Error Handling</td>
            <td class="status">
                <span class="status-badge passed">PASSED</span>
            </td>
            <td class="severity">
                <span class="severity-badge low">LOW</span>
            </td>
            <td class="details">API error handling working properly</td>
            <td class="execution-time">0.00s</td>
        </tr>
        
        <tr class="result-row failed">
            <td class="test-name">Api Security</td>
            <td class="status">
                <span class="status-badge failed">FAILED</span>
            </td>
            <td class="severity">
                <span class="severity-badge high">HIGH</span>
            </td>
            <td class="details">Sensitive data in request body: http://localhost:3001/api/auth/callback/credentials</td>
            <td class="execution-time">0.00s</td>
        </tr>
        
                        </tbody>
                    </table>
                </div>
            </div>
            
        </section>
        
        
        <footer class="report-footer">
            <p>Generated by testerat Enhanced - Universal Web Testing Framework</p>
            <p>Report ID: 20250620_145831</p>
        </footer>
    </div>
    
    <script>
        
        document.addEventListener('DOMContentLoaded', function() {
            // Results Distribution Chart
            const resultsCtx = document.getElementById('resultsChart');
            if (resultsCtx) {
                new Chart(resultsCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['Passed', 'Failed', 'Skipped'],
                        datasets: [{
                            data: [
                                document.querySelectorAll('.status-passed').length,
                                document.querySelectorAll('.status-failed').length,
                                document.querySelectorAll('.status-skipped').length
                            ],
                            backgroundColor: ['#28a745', '#dc3545', '#ffc107']
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: { position: 'bottom' }
                        }
                    }
                });
            }

            // Severity Breakdown Chart
            const severityCtx = document.getElementById('severityChart');
            if (severityCtx) {
                new Chart(severityCtx, {
                    type: 'bar',
                    data: {
                        labels: ['Critical', 'High', 'Medium', 'Low'],
                        datasets: [{
                            label: 'Issues by Severity',
                            data: [
                                document.querySelectorAll('.severity-critical').length,
                                document.querySelectorAll('.severity-high').length,
                                document.querySelectorAll('.severity-medium').length,
                                document.querySelectorAll('.severity-low').length
                            ],
                            backgroundColor: ['#dc3545', '#fd7e14', '#ffc107', '#28a745']
                        }]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: { beginAtZero: true }
                        }
                    }
                });
            }

            // Category Performance Chart
            const categoryCtx = document.getElementById('categoryChart');
            if (categoryCtx) {
                const categories = {};
                document.querySelectorAll('.category-section').forEach(section => {
                    const title = section.querySelector('h3').textContent;
                    const categoryName = title.split(' ')[0];
                    const passed = section.querySelectorAll('.status-passed').length;
                    const failed = section.querySelectorAll('.status-failed').length;
                    categories[categoryName] = { passed, failed };
                });

                new Chart(categoryCtx, {
                    type: 'horizontalBar',
                    data: {
                        labels: Object.keys(categories),
                        datasets: [{
                            label: 'Passed',
                            data: Object.values(categories).map(c => c.passed),
                            backgroundColor: '#28a745'
                        }, {
                            label: 'Failed',
                            data: Object.values(categories).map(c => c.failed),
                            backgroundColor: '#dc3545'
                        }]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            x: { stacked: true },
                            y: { stacked: true }
                        }
                    }
                });
            }

            console.log('testerat Enhanced Report loaded with interactive charts');
        });
        
    </script>
</body>
</html>
        
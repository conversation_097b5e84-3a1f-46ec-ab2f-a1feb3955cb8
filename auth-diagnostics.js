#!/usr/bin/env node

/**
 * Comprehensive Authentication Diagnostics Script
 * Tests authentication flow, session management, and identifies specific issues
 */

const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

class AuthDiagnostics {
  constructor() {
    this.baseUrl = 'http://localhost:3001';
    this.testUser = {
      email: '<EMAIL>',
      password: 'testpassword'
    };
    this.results = {
      timestamp: new Date().toISOString(),
      tests: [],
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
        critical: 0
      }
    };
  }

  async runDiagnostics() {
    console.log('🔍 Starting Authentication Diagnostics...\n');
    
    const browser = await chromium.launch({ headless: false });
    const context = await browser.newContext();
    const page = await context.newPage();

    // Enable request/response logging
    page.on('request', request => {
      if (request.url().includes('/api/auth')) {
        console.log(`📤 AUTH REQUEST: ${request.method()} ${request.url()}`);
      }
    });

    page.on('response', response => {
      if (response.url().includes('/api/auth')) {
        console.log(`📥 AUTH RESPONSE: ${response.status()} ${response.url()}`);
      }
    });

    try {
      // Test 1: Check homepage accessibility
      await this.testHomepageAccess(page);
      
      // Test 2: Check authentication state on homepage
      await this.testUnauthenticatedState(page);
      
      // Test 3: Test login flow
      await this.testLoginFlow(page);
      
      // Test 4: Check authenticated state
      await this.testAuthenticatedState(page);
      
      // Test 5: Test protected route access
      await this.testProtectedRouteAccess(page);
      
      // Test 6: Test session persistence
      await this.testSessionPersistence(page);
      
      // Test 7: Test logout functionality
      await this.testLogoutFlow(page);
      
      // Test 8: Test API authentication
      await this.testApiAuthentication(page);

    } catch (error) {
      console.error('❌ Diagnostic error:', error);
      this.addResult('DIAGNOSTIC_ERROR', 'FAILED', `Unexpected error: ${error.message}`, 'CRITICAL');
    } finally {
      await browser.close();
    }

    this.generateReport();
  }

  async testHomepageAccess(page) {
    console.log('🧪 Test 1: Homepage Access');
    try {
      const response = await page.goto(this.baseUrl);
      const status = response.status();
      
      if (status === 200) {
        console.log('✅ Homepage accessible');
        this.addResult('HOMEPAGE_ACCESS', 'PASSED', 'Homepage loads successfully');
      } else {
        console.log(`❌ Homepage returned status: ${status}`);
        this.addResult('HOMEPAGE_ACCESS', 'FAILED', `HTTP ${status}`, 'HIGH');
      }
    } catch (error) {
      console.log(`❌ Homepage access failed: ${error.message}`);
      this.addResult('HOMEPAGE_ACCESS', 'FAILED', error.message, 'CRITICAL');
    }
  }

  async testUnauthenticatedState(page) {
    console.log('\n🧪 Test 2: Unauthenticated State');
    try {
      // Check for login/signup buttons
      const loginButton = await page.locator('[aria-label="Log in to your account"]').first();
      const signupLink = await page.locator('[aria-label="Create a new account"]').first();
      
      const loginVisible = await loginButton.isVisible();
      const signupVisible = await signupLink.isVisible();
      
      // Check that authenticated-only elements are NOT visible
      const logoutButton = await page.locator('[aria-label="Sign out of your account"]').first();
      const profileLink = await page.locator('[aria-label="View your profile"]').first();
      
      const logoutVisible = await logoutButton.isVisible().catch(() => false);
      const profileVisible = await profileLink.isVisible().catch(() => false);
      
      if (loginVisible && signupVisible && !logoutVisible && !profileVisible) {
        console.log('✅ Unauthenticated state correct');
        this.addResult('UNAUTHENTICATED_STATE', 'PASSED', 'Shows login/signup, hides authenticated elements');
      } else {
        console.log('❌ Unauthenticated state incorrect');
        console.log(`  Login visible: ${loginVisible}, Signup visible: ${signupVisible}`);
        console.log(`  Logout visible: ${logoutVisible}, Profile visible: ${profileVisible}`);
        this.addResult('UNAUTHENTICATED_STATE', 'FAILED', 'Authentication state inconsistent', 'HIGH');
      }
    } catch (error) {
      console.log(`❌ Unauthenticated state test failed: ${error.message}`);
      this.addResult('UNAUTHENTICATED_STATE', 'FAILED', error.message, 'HIGH');
    }
  }

  async testLoginFlow(page) {
    console.log('\n🧪 Test 3: Login Flow');
    try {
      // Navigate to login page
      await page.goto(`${this.baseUrl}/login`);
      await page.waitForLoadState('networkidle');
      
      // Fill login form
      await page.fill('input[type="email"]', this.testUser.email);
      await page.fill('input[type="password"]', this.testUser.password);
      
      // Submit form and wait for navigation
      const [response] = await Promise.all([
        page.waitForResponse(response => response.url().includes('/api/auth/callback')),
        page.click('button[type="submit"]')
      ]);
      
      console.log(`📥 Login response status: ${response.status()}`);
      
      // Wait for potential redirect
      await page.waitForTimeout(2000);
      
      const currentUrl = page.url();
      console.log(`📍 Current URL after login: ${currentUrl}`);
      
      if (response.status() === 200 && !currentUrl.includes('/login')) {
        console.log('✅ Login flow successful');
        this.addResult('LOGIN_FLOW', 'PASSED', 'Login completed successfully');
      } else {
        console.log('❌ Login flow failed');
        this.addResult('LOGIN_FLOW', 'FAILED', `Response: ${response.status()}, URL: ${currentUrl}`, 'CRITICAL');
      }
    } catch (error) {
      console.log(`❌ Login flow test failed: ${error.message}`);
      this.addResult('LOGIN_FLOW', 'FAILED', error.message, 'CRITICAL');
    }
  }

  async testAuthenticatedState(page) {
    console.log('\n🧪 Test 4: Authenticated State');
    try {
      // Go to homepage to check authenticated state
      await page.goto(this.baseUrl);
      await page.waitForLoadState('networkidle');
      
      // Check for authenticated elements
      const logoutButton = await page.locator('[aria-label="Sign out of your account"]').first();
      const profileLink = await page.locator('[aria-label="View your profile"]').first();
      
      // Check that unauthenticated elements are NOT visible
      const loginButton = await page.locator('[aria-label="Log in to your account"]').first();
      
      const logoutVisible = await logoutButton.isVisible().catch(() => false);
      const profileVisible = await profileLink.isVisible().catch(() => false);
      const loginVisible = await loginButton.isVisible().catch(() => false);
      
      if (logoutVisible && profileVisible && !loginVisible) {
        console.log('✅ Authenticated state correct');
        this.addResult('AUTHENTICATED_STATE', 'PASSED', 'Shows authenticated elements, hides login');
      } else {
        console.log('❌ Authenticated state incorrect');
        console.log(`  Logout visible: ${logoutVisible}, Profile visible: ${profileVisible}, Login visible: ${loginVisible}`);
        this.addResult('AUTHENTICATED_STATE', 'FAILED', 'Authentication state inconsistent', 'HIGH');
      }
    } catch (error) {
      console.log(`❌ Authenticated state test failed: ${error.message}`);
      this.addResult('AUTHENTICATED_STATE', 'FAILED', error.message, 'HIGH');
    }
  }

  async testProtectedRouteAccess(page) {
    console.log('\n🧪 Test 5: Protected Route Access');
    try {
      // Try to access dashboard
      const response = await page.goto(`${this.baseUrl}/dashboard`);
      await page.waitForLoadState('networkidle');
      
      const currentUrl = page.url();
      const status = response.status();
      
      console.log(`📍 Dashboard access - Status: ${status}, URL: ${currentUrl}`);
      
      if (status === 200 && currentUrl.includes('/dashboard')) {
        console.log('✅ Protected route access successful');
        this.addResult('PROTECTED_ROUTE_ACCESS', 'PASSED', 'Dashboard accessible when authenticated');
      } else if (currentUrl.includes('/login')) {
        console.log('❌ Redirected to login (authentication not working)');
        this.addResult('PROTECTED_ROUTE_ACCESS', 'FAILED', 'Authenticated user redirected to login', 'HIGH');
      } else {
        console.log('❌ Unexpected behavior');
        this.addResult('PROTECTED_ROUTE_ACCESS', 'FAILED', `Status: ${status}, URL: ${currentUrl}`, 'HIGH');
      }
    } catch (error) {
      console.log(`❌ Protected route test failed: ${error.message}`);
      this.addResult('PROTECTED_ROUTE_ACCESS', 'FAILED', error.message, 'HIGH');
    }
  }

  async testSessionPersistence(page) {
    console.log('\n🧪 Test 6: Session Persistence');
    try {
      // Reload the page to test session persistence
      await page.reload();
      await page.waitForLoadState('networkidle');
      
      // Check if still authenticated after reload
      const logoutButton = await page.locator('[aria-label="Sign out of your account"]').first();
      const logoutVisible = await logoutButton.isVisible().catch(() => false);
      
      if (logoutVisible) {
        console.log('✅ Session persisted across reload');
        this.addResult('SESSION_PERSISTENCE', 'PASSED', 'Session maintained after page reload');
      } else {
        console.log('❌ Session not persisted');
        this.addResult('SESSION_PERSISTENCE', 'FAILED', 'Session lost after page reload', 'HIGH');
      }
    } catch (error) {
      console.log(`❌ Session persistence test failed: ${error.message}`);
      this.addResult('SESSION_PERSISTENCE', 'FAILED', error.message, 'HIGH');
    }
  }

  async testLogoutFlow(page) {
    console.log('\n🧪 Test 7: Logout Flow');
    try {
      // Find and click logout button
      const logoutButton = await page.locator('[aria-label="Sign out of your account"]').first();
      
      if (await logoutButton.isVisible()) {
        await logoutButton.click();
        await page.waitForTimeout(2000);
        
        // Check if redirected and login button is visible
        const loginButton = await page.locator('[aria-label="Log in to your account"]').first();
        const loginVisible = await loginButton.isVisible().catch(() => false);
        
        if (loginVisible) {
          console.log('✅ Logout successful');
          this.addResult('LOGOUT_FLOW', 'PASSED', 'Logout completed successfully');
        } else {
          console.log('❌ Logout failed - still appears authenticated');
          this.addResult('LOGOUT_FLOW', 'FAILED', 'Still authenticated after logout', 'HIGH');
        }
      } else {
        console.log('❌ Logout button not found');
        this.addResult('LOGOUT_FLOW', 'FAILED', 'Logout button not accessible', 'MEDIUM');
      }
    } catch (error) {
      console.log(`❌ Logout flow test failed: ${error.message}`);
      this.addResult('LOGOUT_FLOW', 'FAILED', error.message, 'MEDIUM');
    }
  }

  async testApiAuthentication(page) {
    console.log('\n🧪 Test 8: API Authentication');
    try {
      // Test API endpoint that requires authentication
      const response = await page.evaluate(async () => {
        try {
          const res = await fetch('/api/user/profile', {
            method: 'GET',
            credentials: 'include'
          });
          return {
            status: res.status,
            ok: res.ok,
            statusText: res.statusText
          };
        } catch (error) {
          return {
            error: error.message
          };
        }
      });
      
      console.log(`📥 API response:`, response);
      
      if (response.ok || response.status === 404) {
        console.log('✅ API authentication working');
        this.addResult('API_AUTHENTICATION', 'PASSED', 'API accepts authenticated requests');
      } else if (response.status === 401) {
        console.log('❌ API authentication failed');
        this.addResult('API_AUTHENTICATION', 'FAILED', 'API returns 401 for authenticated user', 'HIGH');
      } else {
        console.log('❓ API authentication unclear');
        this.addResult('API_AUTHENTICATION', 'FAILED', `Unexpected API response: ${response.status}`, 'MEDIUM');
      }
    } catch (error) {
      console.log(`❌ API authentication test failed: ${error.message}`);
      this.addResult('API_AUTHENTICATION', 'FAILED', error.message, 'MEDIUM');
    }
  }

  addResult(testName, status, details, severity = 'LOW') {
    const result = {
      testName,
      status,
      details,
      severity,
      timestamp: new Date().toISOString()
    };
    
    this.results.tests.push(result);
    this.results.summary.total++;
    
    if (status === 'PASSED') {
      this.results.summary.passed++;
    } else {
      this.results.summary.failed++;
      if (severity === 'CRITICAL') {
        this.results.summary.critical++;
      }
    }
  }

  generateReport() {
    console.log('\n📊 AUTHENTICATION DIAGNOSTICS REPORT');
    console.log('=====================================');
    
    console.log(`\n📈 SUMMARY:`);
    console.log(`Total Tests: ${this.results.summary.total}`);
    console.log(`Passed: ${this.results.summary.passed}`);
    console.log(`Failed: ${this.results.summary.failed}`);
    console.log(`Critical Issues: ${this.results.summary.critical}`);
    console.log(`Success Rate: ${((this.results.summary.passed / this.results.summary.total) * 100).toFixed(1)}%`);
    
    console.log(`\n🔍 DETAILED RESULTS:`);
    this.results.tests.forEach(test => {
      const icon = test.status === 'PASSED' ? '✅' : '❌';
      const severity = test.severity !== 'LOW' ? ` [${test.severity}]` : '';
      console.log(`${icon} ${test.testName}${severity}: ${test.details}`);
    });
    
    // Save detailed report
    const reportPath = path.join(__dirname, `auth-diagnostics-${Date.now()}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(this.results, null, 2));
    console.log(`\n📄 Detailed report saved to: ${reportPath}`);
    
    // Generate recommendations
    this.generateRecommendations();
  }

  generateRecommendations() {
    console.log(`\n🎯 RECOMMENDATIONS:`);
    
    const failedTests = this.results.tests.filter(t => t.status === 'FAILED');
    const criticalIssues = failedTests.filter(t => t.severity === 'CRITICAL');
    const highIssues = failedTests.filter(t => t.severity === 'HIGH');
    
    if (criticalIssues.length > 0) {
      console.log(`\n🚨 CRITICAL ISSUES (Fix Immediately):`);
      criticalIssues.forEach((test, i) => {
        console.log(`${i + 1}. ${test.testName}: ${test.details}`);
      });
    }
    
    if (highIssues.length > 0) {
      console.log(`\n⚠️  HIGH PRIORITY ISSUES:`);
      highIssues.forEach((test, i) => {
        console.log(`${i + 1}. ${test.testName}: ${test.details}`);
      });
    }
    
    // Specific recommendations based on common patterns
    if (failedTests.some(t => t.testName.includes('AUTHENTICATED_STATE'))) {
      console.log(`\n💡 Check NextAuth session configuration and SessionProvider setup`);
    }
    
    if (failedTests.some(t => t.testName.includes('PROTECTED_ROUTE'))) {
      console.log(`\n💡 Review middleware.ts and protected route configuration`);
    }
    
    if (failedTests.some(t => t.testName.includes('SESSION_PERSISTENCE'))) {
      console.log(`\n💡 Check cookie settings and JWT configuration in auth.tsx`);
    }
  }
}

// Run diagnostics
async function main() {
  const diagnostics = new AuthDiagnostics();
  await diagnostics.runDiagnostics();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = AuthDiagnostics;

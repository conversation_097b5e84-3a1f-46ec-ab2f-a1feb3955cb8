{"timestamp": "2025-06-20T13:21:15.025Z", "tests": [{"testName": "HOMEPAGE_ACCESS", "status": "PASSED", "details": "Homepage loads successfully", "severity": "LOW", "timestamp": "2025-06-20T13:21:19.430Z"}, {"testName": "UNAUTHENTICATED_STATE", "status": "PASSED", "details": "Shows login/signup, hides authenticated elements", "severity": "LOW", "timestamp": "2025-06-20T13:21:19.509Z"}, {"testName": "LOGIN_FLOW", "status": "PASSED", "details": "<PERSON><PERSON> completed successfully", "severity": "LOW", "timestamp": "2025-06-20T13:21:27.596Z"}, {"testName": "AUTHENTICATED_STATE", "status": "PASSED", "details": "Shows authenticated elements, hides login", "severity": "LOW", "timestamp": "2025-06-20T13:21:29.081Z"}, {"testName": "PROTECTED_ROUTE_ACCESS", "status": "PASSED", "details": "Dashboard accessible when authenticated", "severity": "LOW", "timestamp": "2025-06-20T13:21:34.741Z"}, {"testName": "SESSION_PERSISTENCE", "status": "PASSED", "details": "Session maintained after page reload", "severity": "LOW", "timestamp": "2025-06-20T13:21:37.936Z"}, {"testName": "LOGOUT_FLOW", "status": "PASSED", "details": "Logout completed successfully", "severity": "LOW", "timestamp": "2025-06-20T13:21:39.981Z"}, {"testName": "API_AUTHENTICATION", "status": "PASSED", "details": "API accepts authenticated requests", "severity": "LOW", "timestamp": "2025-06-20T13:21:40.630Z"}], "summary": {"total": 8, "passed": 8, "failed": 0, "critical": 0}}
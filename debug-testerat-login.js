#!/usr/bin/env node

/**
 * Debug Testerat Login Process
 * Replicates exactly what testerat does to identify why login fails
 */

const { chromium } = require('playwright');

async function debugTesteratLogin() {
  console.log('🔍 Debugging Testerat Login Process...\n');
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();

  // Enable request/response logging
  page.on('request', request => {
    if (request.url().includes('/api/auth') || request.url().includes('/login')) {
      console.log(`📤 REQUEST: ${request.method()} ${request.url()}`);
      if (request.method() === 'POST') {
        console.log(`   Body: ${request.postData()}`);
      }
    }
  });

  page.on('response', response => {
    if (response.url().includes('/api/auth') || response.url().includes('/login')) {
      console.log(`📥 RESPONSE: ${response.status()} ${response.url()}`);
    }
  });

  try {
    console.log('1️⃣ Navigating to homepage...');
    await page.goto('http://localhost:3001');
    await page.waitForLoadState('networkidle');
    console.log(`   Current URL: ${page.url()}`);

    console.log('\n2️⃣ Checking initial authentication state...');
    const initialLogoutBtn = await page.locator('[aria-label="Sign out of your account"]').first();
    const initialLoginBtn = await page.locator('[aria-label="Log in to your account"]').first();
    
    const initialLogoutVisible = await initialLogoutBtn.isVisible().catch(() => false);
    const initialLoginVisible = await initialLoginBtn.isVisible().catch(() => false);
    
    console.log(`   Logout button visible: ${initialLogoutVisible}`);
    console.log(`   Login button visible: ${initialLoginVisible}`);

    console.log('\n3️⃣ Navigating to login page...');
    await page.goto('http://localhost:3001/login');
    await page.waitForLoadState('networkidle');
    console.log(`   Current URL: ${page.url()}`);

    console.log('\n4️⃣ Finding login form elements (testerat style)...');
    
    // Use testerat's exact selectors
    const emailSelectors = [
      'input[type="email"]',
      'input[name="email"]', 
      '#email'
    ];
    
    const passwordSelectors = [
      'input[type="password"]',
      'input[name="password"]',
      '#password'
    ];
    
    const buttonSelectors = [
      'button[type="submit"]',
      'input[type="submit"]',
      '.login-btn'
    ];

    let emailInput = null;
    let passwordInput = null;
    let submitButton = null;

    // Find email input
    for (const selector of emailSelectors) {
      emailInput = await page.locator(selector).first();
      if (await emailInput.isVisible().catch(() => false)) {
        console.log(`   ✅ Found email input: ${selector}`);
        break;
      }
    }

    // Find password input
    for (const selector of passwordSelectors) {
      passwordInput = await page.locator(selector).first();
      if (await passwordInput.isVisible().catch(() => false)) {
        console.log(`   ✅ Found password input: ${selector}`);
        break;
      }
    }

    // Find submit button
    for (const selector of buttonSelectors) {
      submitButton = await page.locator(selector).first();
      if (await submitButton.isVisible().catch(() => false)) {
        console.log(`   ✅ Found submit button: ${selector}`);
        break;
      }
    }

    if (!emailInput || !passwordInput || !submitButton) {
      console.log('❌ Could not find all required form elements');
      console.log(`   Email input found: ${!!emailInput}`);
      console.log(`   Password input found: ${!!passwordInput}`);
      console.log(`   Submit button found: ${!!submitButton}`);
      return;
    }

    console.log('\n5️⃣ Filling credentials (testerat style)...');
    await emailInput.fill('<EMAIL>');
    await passwordInput.fill('testpassword');
    console.log('   ✅ Credentials filled');

    console.log('\n6️⃣ Submitting form...');
    await submitButton.click();
    console.log('   ✅ Form submitted');

    console.log('\n7️⃣ Waiting for response...');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000); // Same as testerat
    
    console.log(`   Current URL after submit: ${page.url()}`);

    console.log('\n8️⃣ Checking authentication state after login...');
    
    // Check for logout button (indicates successful auth)
    const logoutSelectors = [
      'button:has-text("Sign out")',
      'button:has-text("Logout")', 
      'button:has-text("Log out")',
      'a:has-text("Sign out")',
      'a:has-text("Logout")',
      'a:has-text("Log out")',
      '[aria-label="Sign out of your account"]'
    ];

    let foundLogout = false;
    for (const selector of logoutSelectors) {
      const element = await page.locator(selector).first();
      if (await element.isVisible().catch(() => false)) {
        console.log(`   ✅ Found logout indicator: ${selector}`);
        foundLogout = true;
        break;
      }
    }

    if (!foundLogout) {
      console.log('   ❌ No logout indicators found');
    }

    // Check for login button (indicates failed auth)
    const loginSelectors = [
      'button:has-text("Sign in")',
      'button:has-text("Login")',
      'button:has-text("Log in")',
      'a:has-text("Sign in")',
      'a:has-text("Login")',
      'a:has-text("Log in")',
      '[aria-label="Log in to your account"]'
    ];

    let foundLogin = false;
    for (const selector of loginSelectors) {
      const element = await page.locator(selector).first();
      if (await element.isVisible().catch(() => false)) {
        console.log(`   ⚠️ Still found login indicator: ${selector}`);
        foundLogin = true;
        break;
      }
    }

    console.log('\n9️⃣ Testing protected route access...');
    await page.goto('http://localhost:3001/dashboard');
    await page.waitForLoadState('networkidle');
    console.log(`   Dashboard URL: ${page.url()}`);
    
    if (page.url().includes('/login')) {
      console.log('   ❌ Redirected to login - authentication failed');
    } else if (page.url().includes('/dashboard')) {
      console.log('   ✅ Dashboard accessible - authentication successful');
    } else {
      console.log(`   ❓ Unexpected redirect: ${page.url()}`);
    }

    console.log('\n🔟 Checking session persistence...');
    await page.reload();
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    console.log(`   URL after reload: ${page.url()}`);
    
    // Check auth state after reload
    const logoutAfterReload = await page.locator('[aria-label="Sign out of your account"]').first();
    const logoutVisibleAfterReload = await logoutAfterReload.isVisible().catch(() => false);
    console.log(`   Logout button visible after reload: ${logoutVisibleAfterReload}`);

    console.log('\n📊 SUMMARY:');
    console.log(`   Authentication successful: ${foundLogout && !foundLogin}`);
    console.log(`   Protected route accessible: ${!page.url().includes('/login')}`);
    console.log(`   Session persists: ${logoutVisibleAfterReload}`);

    // Wait for manual inspection
    console.log('\n⏸️ Pausing for manual inspection (press any key to continue)...');
    await page.waitForTimeout(10000);

  } catch (error) {
    console.error('❌ Error during debug:', error);
  } finally {
    await browser.close();
  }
}

// Run the debug
debugTesteratLogin().catch(console.error);

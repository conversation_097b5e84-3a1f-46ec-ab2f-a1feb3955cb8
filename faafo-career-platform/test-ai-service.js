/**
 * Test script for AI service functionality
 * Tests real API calls to verify all improvements are working
 */

const https = require('https');
const http = require('http');

const BASE_URL = 'http://localhost:3000';

// Test data
const testResumeText = `
<PERSON>e
Software Engineer
Email: <EMAIL>
Phone: (*************

EXPERIENCE:
Senior Software Engineer at TechCorp (2020-2024)
- Led development of microservices architecture serving 1M+ users
- Implemented CI/CD pipelines reducing deployment time by 60%
- Mentored 5 junior developers and conducted code reviews

Software Engineer at StartupXYZ (2018-2020)
- Built full-stack web applications using React and Node.js
- Optimized database queries improving performance by 40%
- Collaborated with cross-functional teams in agile environment

SKILLS:
JavaScript, TypeScript, React, Node.js, Python, AWS, Docker, Kubernetes, PostgreSQL, MongoDB

EDUCATION:
Bachelor of Science in Computer Science
University of Technology (2014-2018)
`;

const testSkills = ['JavaScript', 'React', 'Node.js', 'Python', 'AWS', 'Leadership'];

const testInterviewParams = {
  sessionType: 'TECHNICAL_PRACTICE',
  careerPath: 'Software Engineer',
  experienceLevel: 'SENIOR',
  difficulty: 'INTERMEDIATE',
  count: 3,
  focusAreas: ['technical', 'problem-solving']
};

// Helper function to make HTTP requests
function makeRequest(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    if (data) {
      const postData = JSON.stringify(data);
      options.headers['Content-Length'] = Buffer.byteLength(postData);
    }

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve({
            statusCode: res.statusCode,
            data: parsedData
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            data: responseData
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// Test functions
async function testHealthCheck() {
  console.log('\n🔍 Testing AI Service Health Check...');
  try {
    const response = await makeRequest('/api/admin/ai-service-monitor?view=health');
    console.log('Status Code:', response.statusCode);
    console.log('Health Status:', JSON.stringify(response.data, null, 2));
    return response.statusCode === 200;
  } catch (error) {
    console.error('Health check failed:', error.message);
    return false;
  }
}

async function testResumeAnalysis() {
  console.log('\n📄 Testing Resume Analysis...');
  try {
    const response = await makeRequest('/api/ai/resume-analysis', 'POST', {
      resumeText: testResumeText,
      userId: 'test-user-123'
    });
    
    console.log('Status Code:', response.statusCode);
    if (response.data.success) {
      console.log('✅ Resume analysis successful!');
      console.log('Analysis:', JSON.stringify(response.data.data, null, 2));
      return true;
    } else {
      console.log('❌ Resume analysis failed:', response.data.error);
      return false;
    }
  } catch (error) {
    console.error('Resume analysis test failed:', error.message);
    return false;
  }
}

async function testCareerRecommendations() {
  console.log('\n🎯 Testing Career Recommendations...');
  try {
    const response = await makeRequest('/api/ai/career-recommendations', 'POST', {
      assessmentData: { experience: 'senior', interests: ['technology', 'leadership'] },
      currentSkills: testSkills,
      preferences: { workStyle: 'collaborative', industry: 'technology' },
      userId: 'test-user-123'
    });
    
    console.log('Status Code:', response.statusCode);
    if (response.data.success) {
      console.log('✅ Career recommendations successful!');
      console.log('Recommendations:', JSON.stringify(response.data.data, null, 2));
      return true;
    } else {
      console.log('❌ Career recommendations failed:', response.data.error);
      return false;
    }
  } catch (error) {
    console.error('Career recommendations test failed:', error.message);
    return false;
  }
}

async function testInterviewQuestions() {
  console.log('\n❓ Testing Interview Question Generation...');
  try {
    // First create a session
    const sessionResponse = await makeRequest('/api/interview-practice', 'POST', {
      sessionType: 'TECHNICAL_PRACTICE',
      careerPath: 'Software Engineer',
      experienceLevel: 'SENIOR',
      difficulty: 'INTERMEDIATE',
      focusAreas: ['technical', 'problem-solving']
    });

    if (!sessionResponse.data.success) {
      console.log('❌ Session creation failed:', sessionResponse.data.error);
      return false;
    }

    const sessionId = sessionResponse.data.session.id;
    console.log('Session created:', sessionId);

    // Now generate questions
    const questionsResponse = await makeRequest(`/api/interview-practice/${sessionId}/questions`, 'POST', {
      count: 3,
      difficulty: 'INTERMEDIATE'
    });
    
    console.log('Status Code:', questionsResponse.statusCode);
    if (questionsResponse.data.success) {
      console.log('✅ Interview questions generated successfully!');
      console.log('Questions:', JSON.stringify(questionsResponse.data.questions, null, 2));
      return true;
    } else {
      console.log('❌ Interview questions failed:', questionsResponse.data.error);
      return false;
    }
  } catch (error) {
    console.error('Interview questions test failed:', error.message);
    return false;
  }
}

async function testRateLimiting() {
  console.log('\n⏱️ Testing Rate Limiting...');
  try {
    const promises = [];
    // Make multiple rapid requests to test rate limiting
    for (let i = 0; i < 5; i++) {
      promises.push(makeRequest('/api/ai/resume-analysis', 'POST', {
        resumeText: 'Short test resume',
        userId: 'rate-limit-test-user'
      }));
    }

    const responses = await Promise.all(promises);
    const rateLimitedResponses = responses.filter(r => 
      r.statusCode === 429 || (r.data.error && r.data.error.includes('rate limit'))
    );

    if (rateLimitedResponses.length > 0) {
      console.log('✅ Rate limiting is working!');
      console.log(`${rateLimitedResponses.length} requests were rate limited`);
      return true;
    } else {
      console.log('⚠️ Rate limiting may not be working as expected');
      return false;
    }
  } catch (error) {
    console.error('Rate limiting test failed:', error.message);
    return false;
  }
}

async function testInputValidation() {
  console.log('\n🛡️ Testing Input Validation...');
  try {
    // Test with malicious input
    const maliciousInput = '<script>alert("xss")</script>SELECT * FROM users;';
    const response = await makeRequest('/api/ai/resume-analysis', 'POST', {
      resumeText: maliciousInput,
      userId: 'security-test-user'
    });
    
    console.log('Status Code:', response.statusCode);
    if (response.statusCode === 400 || (response.data.error && response.data.error.includes('Invalid'))) {
      console.log('✅ Input validation is working!');
      console.log('Blocked malicious input:', response.data.error);
      return true;
    } else {
      console.log('⚠️ Input validation may not be working properly');
      return false;
    }
  } catch (error) {
    console.error('Input validation test failed:', error.message);
    return false;
  }
}

async function testMonitoringMetrics() {
  console.log('\n📊 Testing Monitoring Metrics...');
  try {
    const response = await makeRequest('/api/admin/ai-service-monitor?view=metrics');
    console.log('Status Code:', response.statusCode);
    if (response.statusCode === 200 && response.data.metrics) {
      console.log('✅ Monitoring metrics are available!');
      console.log('Metrics:', JSON.stringify(response.data.metrics, null, 2));
      return true;
    } else {
      console.log('❌ Monitoring metrics failed');
      return false;
    }
  } catch (error) {
    console.error('Monitoring metrics test failed:', error.message);
    return false;
  }
}

// Main test runner
async function runAllTests() {
  console.log('🚀 Starting AI Service Comprehensive Testing...\n');
  
  const tests = [
    { name: 'Health Check', fn: testHealthCheck },
    { name: 'Resume Analysis', fn: testResumeAnalysis },
    { name: 'Career Recommendations', fn: testCareerRecommendations },
    { name: 'Interview Questions', fn: testInterviewQuestions },
    { name: 'Rate Limiting', fn: testRateLimiting },
    { name: 'Input Validation', fn: testInputValidation },
    { name: 'Monitoring Metrics', fn: testMonitoringMetrics }
  ];

  const results = [];
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      results.push({ name: test.name, passed: result });
      
      // Wait a bit between tests
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error(`Test ${test.name} threw an error:`, error);
      results.push({ name: test.name, passed: false });
    }
  }

  // Summary
  console.log('\n' + '='.repeat(50));
  console.log('📋 TEST RESULTS SUMMARY');
  console.log('='.repeat(50));
  
  const passed = results.filter(r => r.passed).length;
  const total = results.length;
  
  results.forEach(result => {
    const status = result.passed ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} - ${result.name}`);
  });
  
  console.log('\n' + '='.repeat(50));
  console.log(`OVERALL: ${passed}/${total} tests passed (${Math.round(passed/total*100)}%)`);
  console.log('='.repeat(50));
  
  if (passed === total) {
    console.log('🎉 All tests passed! AI service is working correctly.');
  } else {
    console.log('⚠️ Some tests failed. Please check the issues above.');
  }
}

// Run the tests
runAllTests().catch(console.error);

# Resume Builder API Documentation

## Overview

The Resume Builder API provides endpoints for creating, reading, updating, and deleting user resumes. All endpoints require authentication and implement proper authorization to ensure users can only access their own resumes.

## Base URL

```
/api/resume-builder
```

## Authentication

All endpoints require a valid user session. Include the session cookie in requests.

**Authentication Method**: Session-based authentication via NextAuth.js

**Error Response for Unauthenticated Requests**:
```json
{
  "success": false,
  "error": "Not authenticated"
}
```

## Endpoints

### 1. List User Resumes

**Endpoint**: `GET /api/resume-builder`

**Description**: Retrieve all active resumes for the authenticated user.

**Request**:
```http
GET /api/resume-builder
Content-Type: application/json
```

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": "resume-123",
      "title": "Software Engineer Resume",
      "template": "modern",
      "isPublic": false,
      "lastExported": "2024-01-15T10:30:00Z",
      "exportCount": 3,
      "createdAt": "2024-01-01T09:00:00Z",
      "updatedAt": "2024-01-15T10:30:00Z"
    }
  ]
}
```

**Error Responses**:
- `401 Unauthorized`: User not authenticated
- `404 Not Found`: User not found in database
- `500 Internal Server Error`: Server error

---

### 2. Create New Resume

**Endpoint**: `POST /api/resume-builder`

**Description**: Create a new resume for the authenticated user.

**Request**:
```http
POST /api/resume-builder
Content-Type: application/json

{
  "title": "Software Engineer Resume",
  "personalInfo": {
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "phone": "******-0123",
    "location": "San Francisco, CA",
    "website": "https://johndoe.com",
    "linkedIn": "https://linkedin.com/in/johndoe"
  },
  "summary": "Experienced software engineer with 5+ years of experience in full-stack development.",
  "experience": [
    {
      "company": "Tech Corp",
      "position": "Senior Software Engineer",
      "startDate": "2020-01",
      "endDate": "2023-12",
      "description": "Led development of web applications using React and Node.js.",
      "achievements": [
        "Improved application performance by 40%",
        "Mentored 3 junior developers"
      ]
    }
  ],
  "education": [
    {
      "institution": "University of California, Berkeley",
      "degree": "Bachelor of Science",
      "field": "Computer Science",
      "startDate": "2016-09",
      "endDate": "2020-05",
      "gpa": "3.8",
      "honors": "Magna Cum Laude"
    }
  ],
  "skills": [
    {
      "name": "JavaScript",
      "level": "ADVANCED",
      "category": "Programming Languages"
    },
    {
      "name": "React",
      "level": "ADVANCED",
      "category": "Frameworks & Libraries"
    }
  ],
  "template": "modern",
  "isPublic": false
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "resume-456",
    "title": "Software Engineer Resume",
    "personalInfo": { /* ... */ },
    "summary": "Experienced software engineer...",
    "experience": [ /* ... */ ],
    "education": [ /* ... */ ],
    "skills": [ /* ... */ ],
    "template": "modern",
    "isPublic": false,
    "createdAt": "2024-01-16T14:30:00Z",
    "updatedAt": "2024-01-16T14:30:00Z"
  }
}
```

**Validation Rules**:
- `title`: Required, minimum 1 character
- `personalInfo.firstName`: Required, minimum 1 character
- `personalInfo.lastName`: Required, minimum 1 character
- `personalInfo.email`: Required, valid email format
- `personalInfo.website`: Optional, valid URL format
- `personalInfo.linkedIn`: Optional, valid URL format
- `template`: Optional, defaults to "modern"
- `isPublic`: Optional, defaults to false

**Error Responses**:
- `400 Bad Request`: Validation failed
- `401 Unauthorized`: User not authenticated
- `404 Not Found`: User not found
- `500 Internal Server Error`: Server error

---

### 3. Get Specific Resume

**Endpoint**: `GET /api/resume-builder/{id}`

**Description**: Retrieve a specific resume by ID. User must own the resume.

**Request**:
```http
GET /api/resume-builder/resume-123
Content-Type: application/json
```

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "resume-123",
    "title": "Software Engineer Resume",
    "personalInfo": {
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "phone": "******-0123",
      "location": "San Francisco, CA",
      "website": "https://johndoe.com",
      "linkedIn": "https://linkedin.com/in/johndoe"
    },
    "summary": "Experienced software engineer with 5+ years of experience.",
    "experience": [ /* ... */ ],
    "education": [ /* ... */ ],
    "skills": [ /* ... */ ],
    "template": "modern",
    "isPublic": false,
    "createdAt": "2024-01-01T09:00:00Z",
    "updatedAt": "2024-01-15T10:30:00Z"
  }
}
```

**Error Responses**:
- `401 Unauthorized`: User not authenticated
- `404 Not Found`: Resume not found or user doesn't own it
- `500 Internal Server Error`: Server error

---

### 4. Update Resume

**Endpoint**: `PUT /api/resume-builder/{id}`

**Description**: Update an existing resume. User must own the resume.

**Request**:
```http
PUT /api/resume-builder/resume-123
Content-Type: application/json

{
  "title": "Senior Software Engineer Resume",
  "summary": "Highly experienced software engineer with 5+ years of experience in full-stack development and team leadership.",
  "template": "classic"
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "resume-123",
    "title": "Senior Software Engineer Resume",
    "personalInfo": { /* unchanged */ },
    "summary": "Highly experienced software engineer...",
    "experience": [ /* unchanged */ ],
    "education": [ /* unchanged */ ],
    "skills": [ /* unchanged */ ],
    "template": "classic",
    "isPublic": false,
    "createdAt": "2024-01-01T09:00:00Z",
    "updatedAt": "2024-01-16T15:45:00Z"
  }
}
```

**Notes**:
- Only provided fields will be updated
- `updatedAt` timestamp is automatically set
- Validation rules same as create endpoint

**Error Responses**:
- `400 Bad Request`: Validation failed
- `401 Unauthorized`: User not authenticated
- `404 Not Found`: Resume not found or user doesn't own it
- `500 Internal Server Error`: Server error

---

### 5. Delete Resume

**Endpoint**: `DELETE /api/resume-builder/{id}`

**Description**: Soft delete a resume (sets `isActive` to false). User must own the resume.

**Request**:
```http
DELETE /api/resume-builder/resume-123
Content-Type: application/json
```

**Response**:
```json
{
  "success": true,
  "message": "Resume deleted successfully"
}
```

**Notes**:
- This is a soft delete operation
- Resume data is preserved but marked as inactive
- Deleted resumes won't appear in list endpoints

**Error Responses**:
- `401 Unauthorized`: User not authenticated
- `404 Not Found`: Resume not found or user doesn't own it
- `500 Internal Server Error`: Server error

## Rate Limiting

All endpoints implement rate limiting to prevent abuse:

- **GET endpoints**: 100 requests per 15 minutes
- **POST endpoints**: 20 requests per 15 minutes
- **PUT endpoints**: 50 requests per 15 minutes
- **DELETE endpoints**: 20 requests per 15 minutes

**Rate Limit Headers**:
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642694400
```

## CSRF Protection

All state-changing operations (POST, PUT, DELETE) require CSRF token validation.

## Error Handling

### Standard Error Response Format

```json
{
  "success": false,
  "error": "Error message",
  "details": { /* Additional error details if applicable */ }
}
```

### Validation Error Response

```json
{
  "success": false,
  "error": "Validation failed",
  "details": [
    {
      "code": "invalid_type",
      "expected": "string",
      "received": "undefined",
      "path": ["title"],
      "message": "Required"
    },
    {
      "validation": "email",
      "code": "invalid_string",
      "message": "Invalid email",
      "path": ["personalInfo", "email"]
    }
  ]
}
```

## Data Types

### Template Options
- `"modern"` - Clean, contemporary design
- `"classic"` - Traditional, professional layout
- `"minimal"` - Simple, clean design
- `"creative"` - Unique, eye-catching layout

### Skill Levels
- `"BEGINNER"` - Basic understanding
- `"INTERMEDIATE"` - Practical experience
- `"ADVANCED"` - Extensive experience
- `"EXPERT"` - Deep expertise

### Date Format
All dates should be in `YYYY-MM` format (e.g., "2024-01" for January 2024).

## Examples

### Complete Resume Creation Example

```javascript
const resumeData = {
  title: "Full Stack Developer Resume",
  personalInfo: {
    firstName: "Jane",
    lastName: "Smith",
    email: "<EMAIL>",
    phone: "******-0456",
    location: "New York, NY",
    website: "https://janesmith.dev",
    linkedIn: "https://linkedin.com/in/janesmith"
  },
  summary: "Passionate full-stack developer with 3 years of experience building scalable web applications.",
  experience: [
    {
      company: "StartupCo",
      position: "Full Stack Developer",
      startDate: "2021-06",
      endDate: null, // Current position
      description: "Develop and maintain web applications using React, Node.js, and PostgreSQL.",
      achievements: [
        "Built user authentication system serving 10,000+ users",
        "Reduced page load times by 60% through optimization",
        "Implemented CI/CD pipeline reducing deployment time by 80%"
      ]
    }
  ],
  education: [
    {
      institution: "State University",
      degree: "Bachelor of Science",
      field: "Computer Science",
      startDate: "2017-09",
      endDate: "2021-05",
      gpa: "3.7"
    }
  ],
  skills: [
    { name: "React", level: "ADVANCED", category: "Frontend" },
    { name: "Node.js", level: "INTERMEDIATE", category: "Backend" },
    { name: "PostgreSQL", level: "INTERMEDIATE", category: "Database" },
    { name: "Git", level: "ADVANCED", category: "Tools" }
  ],
  template: "modern",
  isPublic: false
};

// Create resume
const response = await fetch('/api/resume-builder', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(resumeData)
});

const result = await response.json();
console.log('Created resume:', result.data);
```

## SDK/Client Libraries

Currently, the API is accessed directly via HTTP requests. Future versions may include:
- JavaScript/TypeScript SDK
- React hooks for easier integration
- OpenAPI specification for code generation

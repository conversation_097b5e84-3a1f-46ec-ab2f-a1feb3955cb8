import { NextRequest, NextResponse } from 'next/server';

// Simple CSRF token generation without database dependency
function generateSimpleCSRFToken(): string {
  return crypto.randomUUID() + '-' + Date.now().toString(36);
}

export async function GET(request: NextRequest) {
  try {
    // Generate a simple CSRF token
    const csrfToken = generateSimpleCSRFToken();

    const response = NextResponse.json(
      {
        success: true,
        csrfToken,
        timestamp: Date.now()
      },
      {
        status: 200,
        headers: {
          'Cache-Control': 'no-store, no-cache, must-revalidate',
          'Pragma': 'no-cache',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET',
          'Access-Control-Allow-Headers': 'Content-Type'
        }
      }
    );

    // Set CSRF token in cookie for validation
    response.cookies.set('csrf-token', csrfToken, {
      httpOnly: false, // Allow JavaScript access for now
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 // 1 hour
    });

    return response;
  } catch (error) {
    console.error('CSRF token generation error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to generate CSRF token' },
      { status: 500 }
    );
  }
}

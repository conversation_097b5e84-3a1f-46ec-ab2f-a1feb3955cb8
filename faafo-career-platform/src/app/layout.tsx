import type { Metadata, Viewport } from "next";
import { Inter, JetBrains_Mono } from "next/font/google";
import "./globals.css";
import SessionWrapper from '@/components/SessionWrapper';
import { ThemeProvider } from "@/components/layout/ThemeProvider";
import VercelAnalyticsWrapper from "@/app/components/layout/VercelAnalyticsWrapper";
import Footer from "@/components/layout/Footer";
import { NavigationBar } from "@/components/layout/NavigationBar";
// import { ErrorBoundaryProvider } from "@/components/providers/ErrorBoundaryProvider";

// Force dynamic rendering to prevent static generation issues
export const dynamic = 'force-dynamic';
export const revalidate = 0;

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

const jetbrainsMono = JetBrains_Mono({
  variable: "--font-jetbrains-mono",
  subsets: ["latin"],
});

// Viewport configuration moved to metadata for compatibility

export const metadata: Metadata = {
  title: "FAAFO Career Platform - Find Your Path to Career Freedom",
  description: "Empowering career transitions through personalized assessments, financial planning, and community support. Take control of your career journey with FAAFO.",
  keywords: "career, freedom, assessment, learning, professional development, career transition",
  authors: [{ name: "FAAFO Team" }],
  robots: "index, follow",
  openGraph: {
    title: "FAAFO Career Platform - Find Your Path to Career Freedom",
    description: "Empowering career transitions through personalized assessments, financial planning, and community support.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "FAAFO Career Platform",
    description: "Find your path to career freedom",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta name="theme-color" content="#3b82f6" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="format-detection" content="telephone=no" />
      </head>
      <body
        className={`${inter.variable} ${jetbrainsMono.variable} antialiased bg-background text-foreground min-h-screen flex flex-col`}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
        >
          <SessionWrapper>
            {/* Skip to main content link for accessibility */}
            <a
              href="#main-content"
              className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-gray-800 focus:text-white focus:rounded-md focus:shadow-lg"
            >
              Skip to main content
            </a>

            {/* Navigation Header */}
            <header role="banner">
              <NavigationBar />
            </header>

            <main id="main-content" role="main" className="flex-grow min-h-0" aria-label="Main content">
              {children}
            </main>

            <footer role="contentinfo">
              <Footer />
            </footer>
          </SessionWrapper>
          <VercelAnalyticsWrapper />
        </ThemeProvider>
      </body>
    </html>
  );
}

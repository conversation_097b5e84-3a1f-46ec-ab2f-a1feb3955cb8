"use client";

import { SessionProvider } from 'next-auth/react';
import React from 'react';

interface SessionWrapperProps {
  children: React.ReactNode;
}

export default function SessionWrapper({
  children,
}: SessionWrapperProps) {
  return (
    <SessionProvider
      refetchInterval={5 * 60} // Refetch every 5 minutes for better persistence
      refetchOnWindowFocus={true} // Refetch when window gains focus
      refetchWhenOffline={false} // Don't refetch when offline
      basePath="/api/auth" // Ensure correct auth path
      baseUrl={typeof window !== 'undefined' ? window.location.origin : undefined}
    >
      {children}
    </SessionProvider>
  );
}

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  BookOpen, 
  Target, 
  MessageSquare, 
  Award, 
  TrendingUp, 
  Clock, 
  Star,
  RefreshCw,
  Calendar,
  BarChart3
} from 'lucide-react';
import { MetricCard } from './MetricCard';
import { LineChart } from './charts/LineChart';
import { BarChart } from './charts/BarChart';

interface PersonalAnalyticsData {
  learning: any;
  career: any;
  community: any;
  goals: any;
  generatedAt: string;
  timeRange: string;
  type: string;
}

export function PersonalDashboard() {
  const [data, setData] = useState<PersonalAnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState('30');
  const [activeTab, setActiveTab] = useState('overview');

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/analytics/dashboard?range=${timeRange}&metric=all&type=personal`);
      const result = await response.json();

      if (!response.ok) {
        // Handle 404 gracefully for new users without data
        if (response.status === 404) {
          setData({
            success: true,
            data: {
              learning: { totalResourcesCompleted: 0, completionRate: 0 },
              career: { activePaths: 0, completedPaths: 0 },
              community: { totalPosts: 0, totalReplies: 0, forumReputation: 0 },
              goals: { achievements: [], completedGoals: 0 }
            }
          });
          return;
        }
        throw new Error(result.error || 'Failed to fetch personal analytics');
      }

      setData(result);
    } catch (err) {
      // Only log non-404 errors to reduce console noise
      if (err instanceof Error && !err.message.includes('404')) {
        console.error('Error fetching personal analytics:', err);
      }
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [timeRange]);

  const handleRefresh = () => {
    fetchData();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-gray-500" />
        <span className="ml-2 text-gray-500">Loading your analytics...</span>
      </div>
    );
  }

  if (error) {
    return (
      <Card className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950">
        <CardContent className="pt-6">
          <div className="flex items-center space-x-2 text-red-600 dark:text-red-400">
            <span className="font-medium">Error:</span>
            <span>{error}</span>
          </div>
          <Button 
            onClick={handleRefresh} 
            variant="outline" 
            size="sm" 
            className="mt-4"
          >
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (!data) {
    return <div>No data available</div>;
  }

  const { learning, career, community, goals } = data.data || data;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            My Learning Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Track your personal learning progress and achievements
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">Last 7 days</SelectItem>
              <SelectItem value="30">Last 30 days</SelectItem>
              <SelectItem value="90">Last 90 days</SelectItem>
              <SelectItem value="365">Last year</SelectItem>
            </SelectContent>
          </Select>
          
          <Button onClick={handleRefresh} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Resources Completed"
          value={learning?.totalResourcesCompleted || 0}
          icon={BookOpen}
          description={learning?.completionRate ? `${learning.completionRate.toFixed(1)}% completion rate` : undefined}
          className="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950"
        />

        <MetricCard
          title="Active Learning Paths"
          value={career?.activePaths || 0}
          icon={Target}
          description={career?.completedPaths ? `${career.completedPaths} completed` : undefined}
          className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950"
        />

        <MetricCard
          title="Forum Contributions"
          value={(community?.totalPosts || 0) + (community?.totalReplies || 0)}
          icon={MessageSquare}
          description={community?.forumReputation ? `${community.forumReputation} reputation` : undefined}
          className="border-purple-200 bg-purple-50 dark:border-purple-800 dark:bg-purple-950"
        />

        <MetricCard
          title="Achievements"
          value={goals?.achievements?.length || 0}
          icon={Award}
          description={goals?.completedGoals ? `${goals.completedGoals} goals completed` : undefined}
          className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950"
        />
      </div>

      {/* Detailed Analytics Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="learning">Learning</TabsTrigger>
          <TabsTrigger value="career">Career Paths</TabsTrigger>
          <TabsTrigger value="community">Community</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Learning Activity Chart */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <TrendingUp className="h-5 w-5 mr-2 text-blue-600" />
                  Learning Activity
                </CardTitle>
                <CardDescription>
                  Your daily learning progress over time
                </CardDescription>
              </CardHeader>
              <CardContent>
                {learning?.recentActivity && (
                  <LineChart
                    data={learning.recentActivity}
                    xAxisKey="date"
                    lines={[
                      { key: 'resourcesCompleted', name: 'Resources Completed', color: '#3b82f6' }
                    ]}
                    title="Resources Completed"
                  />
                )}
              </CardContent>
            </Card>

            {/* Recent Achievements */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Award className="h-5 w-5 mr-2 text-orange-600" />
                  Recent Achievements
                </CardTitle>
                <CardDescription>
                  Your latest accomplishments
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {goals?.achievements?.slice(0, 5).map((achievement: any) => (
                    <div key={achievement.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <div>
                        <h4 className="font-medium text-sm">{achievement.title}</h4>
                        <p className="text-xs text-gray-600 dark:text-gray-400">{achievement.description}</p>
                      </div>
                      <Badge variant="secondary">{achievement.points} pts</Badge>
                    </div>
                  )) || (
                    <p className="text-gray-500 text-center py-4">No achievements yet</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="learning" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Favorite Resources */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Star className="h-5 w-5 mr-2 text-yellow-600" />
                  Top Rated Resources
                </CardTitle>
                <CardDescription>
                  Resources you've completed and rated highly
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {learning?.favoriteResources?.map((resource: any) => (
                    <div key={resource.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <div>
                        <h4 className="font-medium text-sm">{resource.title}</h4>
                        <p className="text-xs text-gray-600 dark:text-gray-400">{resource.category}</p>
                      </div>
                      <div className="flex items-center space-x-1">
                        {[...Array(5)].map((_, i) => (
                          <Star 
                            key={i} 
                            className={`h-3 w-3 ${i < resource.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`} 
                          />
                        ))}
                      </div>
                    </div>
                  )) || (
                    <p className="text-gray-500 text-center py-4">Complete and rate resources to see them here</p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Category Progress */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart3 className="h-5 w-5 mr-2 text-green-600" />
                  Category Progress
                </CardTitle>
                <CardDescription>
                  Your progress across different learning categories
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {learning?.categoryProgress?.map((category: any) => (
                    <div key={category.category} className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="font-medium">{category.category}</span>
                        <span className="text-gray-600 dark:text-gray-400">
                          {category.completed}/{category.started}
                        </span>
                      </div>
                      <Progress value={category.completionRate} className="h-2" />
                    </div>
                  )) || (
                    <p className="text-gray-500 text-center py-4">Start learning to see category progress</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="career" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Current Learning Paths */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Target className="h-5 w-5 mr-2 text-blue-600" />
                  Active Learning Paths
                </CardTitle>
                <CardDescription>
                  Your current learning path progress
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {career?.currentPathProgress?.map((path: any) => (
                    <div key={path.pathId} className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="font-medium">{path.pathName}</span>
                        <span className="text-gray-600 dark:text-gray-400">
                          {path.progress.toFixed(0)}%
                        </span>
                      </div>
                      <Progress value={path.progress} className="h-2" />
                      <div className="flex justify-between text-xs text-gray-500">
                        <span>{path.timeSpent}h spent</span>
                        <span>{path.estimatedHours}h estimated</span>
                      </div>
                    </div>
                  )) || (
                    <p className="text-gray-500 text-center py-4">No active learning paths</p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Skill Progress */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <TrendingUp className="h-5 w-5 mr-2 text-green-600" />
                  Skill Development
                </CardTitle>
                <CardDescription>
                  Your progress in different skills
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {career?.skillProgress?.map((skill: any) => (
                    <div key={skill.skillId} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <div>
                        <h4 className="font-medium text-sm">{skill.skillName}</h4>
                        <p className="text-xs text-gray-600 dark:text-gray-400">
                          Level {skill.currentLevel} • {skill.completedResources} resources
                        </p>
                      </div>
                      <Badge variant="outline">{skill.progressPoints} pts</Badge>
                    </div>
                  )) || (
                    <p className="text-gray-500 text-center py-4">Start learning to develop skills</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="community" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Community Activity */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MessageSquare className="h-5 w-5 mr-2 text-purple-600" />
                  Community Activity
                </CardTitle>
                <CardDescription>
                  Your forum participation over time
                </CardDescription>
              </CardHeader>
              <CardContent>
                {community?.recentActivity && (
                  <LineChart
                    data={community.recentActivity}
                    xAxisKey="date"
                    lines={[
                      { key: 'posts', name: 'Posts & Replies', color: '#8b5cf6' }
                    ]}
                    title="Posts & Replies"
                  />
                )}
              </CardContent>
            </Card>

            {/* Community Stats */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Award className="h-5 w-5 mr-2 text-orange-600" />
                  Community Standing
                </CardTitle>
                <CardDescription>
                  Your reputation and contributions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Forum Reputation</span>
                    <Badge variant="secondary">{community?.forumReputation || 0}</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Total Posts</span>
                    <span className="text-sm text-gray-600 dark:text-gray-400">{community?.totalPosts || 0}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Total Replies</span>
                    <span className="text-sm text-gray-600 dark:text-gray-400">{community?.totalReplies || 0}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Reactions Given</span>
                    <span className="text-sm text-gray-600 dark:text-gray-400">{community?.totalReactions || 0}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

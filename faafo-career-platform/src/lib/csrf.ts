import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from './auth';

// Simple in-memory token storage for CSRF tokens
const csrfTokens = new Map<string, { token: string; expiresAt: number }>();

export function generateCSRFToken(): string {
  return crypto.randomUUID();
}

export async function getCSRFToken(request: NextRequest): Promise<string> {
  // Get user session to create a unique identifier
  const session = await getServerSession(authOptions);
  const userId = session?.user?.id;

  if (!userId) {
    // For non-authenticated users, use IP address
    const ip = request.headers.get('x-forwarded-for') ||
               request.headers.get('x-real-ip') ||
               'anonymous';
    const identifier = `guest_${ip}`;

    const existing = csrfTokens.get(identifier);
    if (existing && existing.expiresAt > Date.now()) {
      return existing.token;
    }

    const token = generateCSRFToken();
    csrfTokens.set(identifier, {
      token,
      expiresAt: Date.now() + (60 * 60 * 1000) // 1 hour
    });

    return token;
  }

  // For authenticated users, use user ID
  const existing = csrfTokens.get(userId);
  if (existing && existing.expiresAt > Date.now()) {
    return existing.token;
  }

  const token = generateCSRFToken();
  csrfTokens.set(userId, {
    token,
    expiresAt: Date.now() + (60 * 60 * 1000) // 1 hour
  });

  return token;
}

export async function validateCSRFToken(request: NextRequest, token: string): Promise<boolean> {
  // Get user session to create a unique identifier
  const session = await getServerSession(authOptions);
  const userId = session?.user?.id;

  if (!userId) {
    // For non-authenticated users, use IP address
    const ip = request.headers.get('x-forwarded-for') ||
               request.headers.get('x-real-ip') ||
               'anonymous';
    const identifier = `guest_${ip}`;

    const stored = csrfTokens.get(identifier);
    if (stored && stored.expiresAt > Date.now() && stored.token === token) {
      return true;
    }
    return false;
  }

  // For authenticated users, use user ID
  const stored = csrfTokens.get(userId);
  if (stored && stored.expiresAt > Date.now() && stored.token === token) {
    return true;
  }

  return false;
}

export async function withCSRFProtection(
  request: NextRequest,
  handler: () => Promise<NextResponse>
): Promise<NextResponse> {
  // Skip CSRF for GET requests
  if (request.method === 'GET') {
    return handler();
  }
  
  // Get CSRF token from header or body
  const csrfToken = request.headers.get('x-csrf-token') || 
                   request.headers.get('csrf-token');
  
  if (!csrfToken) {
    return NextResponse.json(
      { error: 'CSRF token missing' },
      { status: 403 }
    );
  }
  
  const isValid = await validateCSRFToken(request, csrfToken);
  if (!isValid) {
    return NextResponse.json(
      { error: 'Invalid CSRF token' },
      { status: 403 }
    );
  }
  
  return handler();
}

// API endpoint to get CSRF token
export async function getCSRFTokenEndpoint(request: NextRequest): Promise<NextResponse> {
  const token = await getCSRFToken(request);
  return NextResponse.json({ csrfToken: token });
}

// Cleanup expired tokens periodically
setInterval(() => {
  const now = Date.now();
  for (const [key, value] of csrfTokens.entries()) {
    if (value.expiresAt <= now) {
      csrfTokens.delete(key);
    }
  }
}, 15 * 60 * 1000); // Clean up every 15 minutes

import { NextRequest, NextResponse } from 'next/server';
import securityStorage from './security-storage';

export function generateCSRFToken(): string {
  return crypto.randomUUID();
}

export async function getCSRFToken(request: NextRequest): Promise<string> {
  // Check if we have a valid token
  const existing = await securityStorage.getCSRFToken(request);
  if (existing) {
    return existing;
  }

  // Generate new token
  const token = generateCSRFToken();
  const expiresAt = new Date(Date.now() + (60 * 60 * 1000)); // 1 hour

  await securityStorage.storeCSRFToken(request, token, expiresAt);

  return token;
}

export async function validateCSRFToken(request: NextRequest, token: string): Promise<boolean> {
  return await securityStorage.validateCSRFToken(request, token);
}

export async function withCSRFProtection(
  request: NextRequest,
  handler: () => Promise<NextResponse>
): Promise<NextResponse> {
  // Skip CSRF for GET requests
  if (request.method === 'GET') {
    return handler();
  }
  
  // Get CSRF token from header or body
  const csrfToken = request.headers.get('x-csrf-token') || 
                   request.headers.get('csrf-token');
  
  if (!csrfToken) {
    return NextResponse.json(
      { error: 'CSRF token missing' },
      { status: 403 }
    );
  }
  
  const isValid = await validateCSRFToken(request, csrfToken);
  if (!isValid) {
    return NextResponse.json(
      { error: 'Invalid CSRF token' },
      { status: 403 }
    );
  }
  
  return handler();
}

// API endpoint to get CSRF token
export async function getCSRFTokenEndpoint(request: NextRequest): Promise<NextResponse> {
  const token = await getCSRFToken(request);
  return NextResponse.json({ csrfToken: token });
}

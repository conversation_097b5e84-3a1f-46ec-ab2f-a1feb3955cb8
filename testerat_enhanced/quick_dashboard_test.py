#!/usr/bin/env python3
"""
Quick Dashboard Tab Test
Focused test to verify tab functionality works correctly
"""

from playwright.sync_api import sync_playwright
import time

def test_login(page):
    """Helper function to login"""
    print("🔐 Logging in...")
    page.goto("http://localhost:3001/login")
    page.wait_for_load_state('networkidle')
    
    email_input = page.query_selector("input[id='email']")
    password_input = page.query_selector("input[id='password']")
    submit_btn = page.query_selector("button[type='submit']")
    
    if all([email_input, password_input, submit_btn]):
        email_input.fill("<EMAIL>")
        password_input.fill("testpassword")
        submit_btn.click()
        page.wait_for_load_state('networkidle')
        time.sleep(2)
        print("✅ Login successful")
        return True
    else:
        print("❌ Login failed - elements not found")
        return False

def test_dashboard_tabs(page):
    """Test dashboard tab functionality"""
    print("\n🏠 TESTING DASHBOARD TABS")
    print("=" * 40)
    
    # Navigate to dashboard
    page.goto("http://localhost:3001/dashboard")
    page.wait_for_load_state('networkidle')
    time.sleep(3)
    
    # Test 1: Check tab structure
    print("   🎯 Checking tab structure...")
    tabs = page.query_selector_all('[role="tablist"] button')
    print(f"   Found {len(tabs)} tabs")
    
    for i, tab in enumerate(tabs):
        text = tab.text_content().strip()
        value = tab.get_attribute('data-value') or tab.get_attribute('value')
        print(f"   Tab {i+1}: '{text}' (value: {value})")
    
    # Test 2: Click each tab and verify content changes
    tab_tests = [
        ('overview', 'Overview'),
        ('progress', 'Progress'),
        ('goals', 'Goals'),
        ('achievements', 'Achievements'),
        ('analytics', 'Analytics')
    ]
    
    for tab_value, tab_name in tab_tests:
        print(f"   🎯 Testing {tab_name} tab...")
        try:
            # Find and click tab by value attribute
            tab_selector = f'button[data-value="{tab_value}"], button[value="{tab_value}"]'
            tab_button = page.query_selector(tab_selector)
            
            if not tab_button:
                # Try alternative selectors
                tab_button = page.query_selector(f'button:has-text("{tab_name}")')
            
            if tab_button:
                tab_button.click()
                page.wait_for_timeout(2000)
                
                # Check if content changed
                active_content = page.query_selector('[data-state="active"]')
                if active_content:
                    print(f"   ✅ {tab_name} tab activated successfully")
                else:
                    print(f"   ❌ {tab_name} tab content not found")
            else:
                print(f"   ❌ {tab_name} tab button not found")
                
        except Exception as e:
            print(f"   ❌ {tab_name} tab test failed: {e}")
    
    # Test 3: URL parameter test
    print("   🎯 Testing URL parameters...")
    page.goto("http://localhost:3001/dashboard?tab=analytics")
    page.wait_for_load_state('networkidle')
    time.sleep(2)
    
    active_content = page.query_selector('[data-state="active"]')
    if active_content:
        print("   ✅ URL parameter support working")
    else:
        print("   ❌ URL parameter support not working")

def test_progress_redirect(page):
    """Test progress page redirect"""
    print("\n🔄 TESTING PROGRESS REDIRECT")
    print("=" * 40)
    
    print("   🎯 Testing /progress redirect...")
    page.goto("http://localhost:3001/progress")
    page.wait_for_load_state('networkidle')
    time.sleep(3)
    
    current_url = page.url
    print(f"   Current URL: {current_url}")
    
    if '/dashboard' in current_url:
        print("   ✅ Progress page redirects to dashboard")
        
        # Check if progress tab is active
        if 'tab=progress' in current_url:
            print("   ✅ Redirected to progress tab")
        else:
            print("   ⚠️ Redirected to dashboard but not progress tab")
    else:
        print("   ❌ Progress page didn't redirect properly")

def test_csrf_quick_check(page):
    """Quick CSRF test"""
    print("\n💰 QUICK CSRF CHECK")
    print("=" * 40)
    
    page.goto("http://localhost:3001/tools/salary-calculator")
    page.wait_for_load_state('networkidle')
    time.sleep(2)
    
    # Check for CSRF errors
    console_messages = []
    page.on("console", lambda msg: console_messages.append(msg.text))
    
    page.reload()
    page.wait_for_load_state('networkidle')
    time.sleep(2)
    
    csrf_errors = [msg for msg in console_messages if 'csrf' in msg.lower()]
    
    if not csrf_errors:
        print("   ✅ No CSRF errors found")
    else:
        print(f"   ❌ CSRF errors: {csrf_errors}")
    
    # Quick dropdown test
    career_trigger = page.query_selector('[data-testid="career-path-trigger"]')
    if career_trigger:
        career_trigger.click()
        page.wait_for_timeout(1000)
        
        options = page.query_selector_all('[role="option"]')
        if len(options) > 0:
            print(f"   ✅ Career dropdown working ({len(options)} options)")
            # Click first option
            options[0].click()
        else:
            print("   ❌ Career dropdown not working")
    else:
        print("   ❌ Career dropdown trigger not found")

def main():
    print("🧪 QUICK DASHBOARD & CSRF TEST")
    print("=" * 50)
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        
        try:
            # Login first
            if not test_login(page):
                print("❌ Login failed, cannot continue tests")
                return
            
            # Run focused tests
            test_dashboard_tabs(page)
            test_progress_redirect(page)
            test_csrf_quick_check(page)
            
            print("\n" + "=" * 50)
            print("🎯 QUICK TEST COMPLETE")
            print("Check the browser window for visual verification")
            
        except Exception as e:
            print(f"❌ Critical error during testing: {e}")
        finally:
            print("\n🔍 Keeping browser open for 15 seconds for inspection...")
            time.sleep(15)
            browser.close()

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Example: Testing FAAFO Career Platform with Enhanced Testerat

This example demonstrates how to use Enhanced Testerat to test the FAAFO Career Platform
and catch the specific issues we identified:

1. Authentication state issues (useSession loading)
2. Workflow navigation bugs (duplicate case statements)  
3. CSRF token header mismatches

Usage:
    python testerat_enhanced/examples/test_faafo.py
"""

import sys
import os
from pathlib import Path

# Add the parent directory to the path so we can import testerat_enhanced
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from testerat_enhanced import EnhancedTesterat, UniversalTestConfig, FrameworkType


def test_faafo_career_platform():
    """Test FAAFO Career Platform with Enhanced Testerat"""
    
    print("🎯 Enhanced Testerat - FAAFO Career Platform Testing")
    print("=" * 60)
    print()
    
    # Create configuration optimized for FAAFO (Next.js app)
    config = UniversalTestConfig()
    
    # Framework-specific optimization
    config.framework = FrameworkType.NEXTJS
    
    # Authentication configuration for FAAFO
    config.auth_config.auth_type = "nextauth"
    config.auth_config.login_url = "/login"
    config.auth_config.logout_url = "/logout"
    config.auth_config.protected_routes = [
        "/dashboard",
        "/profile", 
        "/tools/interview-practice",
        "/tools/salary-calculator"
    ]
    
    # Test user credentials (use the known test user)
    config.auth_config.test_users = {
        "standard": {
            "email": "<EMAIL>",
            "password": "testpassword"
        }
    }
    
    # Workflow configuration for Interview Practice wizard
    config.workflow_config.step_timeout = 10000
    config.workflow_config.navigation_timeout = 5000
    config.workflow_config.form_submission_timeout = 15000
    
    # API configuration for CSRF testing
    config.api_config.csrf_header_variations = [
        "X-CSRF-Token",  # Correct format
        "x-csrf-token",  # The problematic format we found
        "csrf-token"
    ]
    
    # Enable all testing modules
    config.test_authentication = True
    config.test_workflows = True
    config.test_api_interactions = True
    config.test_security = True
    config.test_accessibility = True
    
    # Detailed logging for debugging
    config.detailed_logging = True
    
    # Use visible browser for demonstration (set to True for headless)
    config.headless = False
    
    print("🔧 Configuration:")
    print(f"   Framework: {config.framework.value}")
    print(f"   Authentication: NextAuth")
    print(f"   Test User: {config.auth_config.test_users['standard']['email']}")
    print(f"   Browser Mode: {'Headless' if config.headless else 'Visible'}")
    print()
    
    try:
        # Initialize Enhanced Testerat
        testerat = EnhancedTesterat(config)
        
        # Test local development server
        url = "http://localhost:3001"
        description = "FAAFO Career Platform - Critical Issue Detection"
        
        print(f"🚀 Starting comprehensive testing of {url}")
        print()
        
        # Run comprehensive testing
        results = testerat.run_comprehensive_test(url, description)
        
        # Analyze results
        print()
        print("📊 TEST RESULTS ANALYSIS")
        print("=" * 40)
        
        summary = results['summary']
        critical_issues = results['critical_issues']
        
        print(f"✅ Total Tests: {summary['total_tests']}")
        print(f"✅ Passed: {summary['passed']}")
        print(f"❌ Failed: {summary['failed']}")
        print(f"🚨 Critical Issues: {len(critical_issues)}")
        print(f"📈 Success Rate: {summary['success_rate']:.1f}%")
        print(f"⏱️  Execution Time: {summary['total_execution_time']:.2f}s")
        print()
        
        # Check for the specific issues we're looking for
        print("🔍 CHECKING FOR KNOWN ISSUES")
        print("=" * 35)
        
        known_issues_found = []
        
        for issue in critical_issues:
            test_name = issue['test_name']
            details = issue['details'].lower()
            
            # Check for authentication state issue
            if 'auth' in test_name and ('loading state' in details or 'logged-out content' in details):
                known_issues_found.append("✅ Authentication State Issue Detected")
                print("🚨 Found: Authentication state issue (useSession loading)")
                print(f"   Details: {issue['details']}")
                print(f"   Fix: {issue['recommendations'][0] if issue['recommendations'] else 'See recommendations'}")
                print()
            
            # Check for workflow navigation issue
            if 'workflow' in test_name or 'navigation' in test_name:
                if 'navigation failed' in details or 'duplicate case' in details:
                    known_issues_found.append("✅ Workflow Navigation Bug Detected")
                    print("🚨 Found: Workflow navigation bug (duplicate case statements)")
                    print(f"   Details: {issue['details']}")
                    print(f"   Fix: {issue['recommendations'][0] if issue['recommendations'] else 'See recommendations'}")
                    print()
            
            # Check for CSRF token issue
            if 'csrf' in test_name and ('403' in details or 'header' in details):
                known_issues_found.append("✅ CSRF Token Issue Detected")
                print("🚨 Found: CSRF token header case mismatch")
                print(f"   Details: {issue['details']}")
                print(f"   Fix: {issue['recommendations'][0] if issue['recommendations'] else 'See recommendations'}")
                print()
        
        # Summary of known issue detection
        print("📋 KNOWN ISSUE DETECTION SUMMARY")
        print("=" * 40)
        
        expected_issues = [
            "Authentication State Issue (useSession loading)",
            "Workflow Navigation Bug (duplicate case statements)",
            "CSRF Token Issue (header case mismatch)"
        ]
        
        for expected in expected_issues:
            found = any(expected.split('(')[0].strip() in found_issue for found_issue in known_issues_found)
            status = "✅ DETECTED" if found else "❌ NOT FOUND"
            print(f"{status}: {expected}")
        
        print()
        
        # Show generated reports
        print("📄 GENERATED REPORTS")
        print("=" * 20)
        for report_type, file_path in results['report_files'].items():
            if file_path:
                print(f"📊 {report_type.upper()}: {file_path}")
        print()
        
        # Final assessment
        if len(critical_issues) > 0:
            print("🎯 ENHANCED TESTERAT VALIDATION: SUCCESS!")
            print("Enhanced Testerat successfully detected critical issues in the FAAFO Career Platform.")
            print("This demonstrates the framework's ability to catch real production bugs.")
        else:
            print("ℹ️  No critical issues detected.")
            print("This could mean:")
            print("1. The issues have been fixed")
            print("2. The application is not running")
            print("3. The test scenarios need adjustment")
        
        return results
        
    except Exception as e:
        print(f"❌ Testing failed: {str(e)}")
        print()
        print("💡 Troubleshooting:")
        print("1. Make sure the FAAFO Career Platform is running on http://localhost:3001")
        print("2. Check that the test user credentials are correct")
        print("3. Ensure all dependencies are installed")
        return None


def main():
    """Main function"""
    print("🎯 Enhanced Testerat - FAAFO Career Platform Example")
    print()
    print("This example demonstrates Enhanced Testerat's ability to detect")
    print("real production issues in the FAAFO Career Platform:")
    print()
    print("1. 🔐 Authentication state bugs (useSession loading)")
    print("2. 🔄 Workflow navigation failures (duplicate case statements)")
    print("3. 🌐 CSRF token header mismatches (x-csrf-token vs X-CSRF-Token)")
    print()
    
    # Check if user wants to proceed
    try:
        response = input("Start testing? (y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            print("Testing cancelled.")
            return
    except KeyboardInterrupt:
        print("\nTesting cancelled.")
        return
    
    print()
    
    # Run the test
    results = test_faafo_career_platform()
    
    if results:
        print()
        print("🎉 Testing completed! Check the generated reports for detailed analysis.")
    else:
        print()
        print("❌ Testing failed. Please check the error messages above.")


if __name__ == '__main__':
    main()

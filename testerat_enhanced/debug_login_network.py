#!/usr/bin/env python3
"""
Debug login with network monitoring
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent))

from playwright.sync_api import sync_playwright
import time

def debug_login_with_network():
    """Debug login process with network monitoring"""
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        
        # Monitor network requests
        requests = []
        responses = []
        
        def handle_request(request):
            requests.append(f"REQUEST: {request.method} {request.url}")
            if request.method == "POST":
                try:
                    post_data = request.post_data
                    if post_data:
                        requests.append(f"  POST DATA: {post_data}")
                except:
                    pass
        
        def handle_response(response):
            responses.append(f"RESPONSE: {response.status} {response.url}")
            if response.status >= 400:
                responses.append(f"  ERROR: {response.status} {response.status_text}")
        
        page.on("request", handle_request)
        page.on("response", handle_response)
        
        print("🔍 DEBUGGING LOGIN WITH NETWORK MONITORING")
        print("=" * 50)
        
        # Navigate to login page
        print("1️⃣ Navigating to login page...")
        page.goto("http://localhost:3002/login")
        page.wait_for_load_state('networkidle')
        
        # Fill and submit form
        print("2️⃣ Filling and submitting form...")
        email_input = page.query_selector("input[id='email']")
        password_input = page.query_selector("input[id='password']")
        submit_btn = page.query_selector("button[type='submit']")
        
        email_input.fill("<EMAIL>")
        password_input.fill("testpassword")
        
        # Clear previous network logs
        requests.clear()
        responses.clear()
        
        print("3️⃣ Submitting form and monitoring network...")
        submit_btn.click()
        
        # Wait for network activity
        time.sleep(5)
        page.wait_for_load_state('networkidle')
        
        print(f"4️⃣ Current URL: {page.url}")
        
        print("\n📡 NETWORK REQUESTS:")
        for req in requests[-10:]:  # Last 10 requests
            print(f"   {req}")
        
        print("\n📡 NETWORK RESPONSES:")
        for resp in responses[-10:]:  # Last 10 responses
            print(f"   {resp}")
        
        # Check authentication state
        print("\n5️⃣ Authentication state:")
        sign_out_btn = page.query_selector("button:has-text('Sign Out')")
        profile_link = page.query_selector("a:has-text('Profile')")
        
        print(f"   Sign Out button: {bool(sign_out_btn)}")
        print(f"   Profile link: {bool(profile_link)}")
        
        # Check for error messages on page
        print("\n6️⃣ Checking for error messages...")
        error_selectors = [
            ".error",
            "[role='alert']", 
            ".text-red-500",
            ".text-red-600",
            ".alert-error",
            "div:has-text('Invalid')",
            "div:has-text('incorrect')",
            "div:has-text('failed')"
        ]
        
        for selector in error_selectors:
            error_elem = page.query_selector(selector)
            if error_elem:
                error_text = error_elem.text_content()
                if error_text and error_text.strip():
                    print(f"   ERROR ({selector}): {error_text}")
        
        print("\n7️⃣ Page title:", page.title())
        
        time.sleep(10)
        browser.close()

if __name__ == "__main__":
    debug_login_with_network()

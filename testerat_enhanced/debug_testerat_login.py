#!/usr/bin/env python3
"""
Debug exactly what testerat is doing during login
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent))

from playwright.sync_api import sync_playwright
import time

def debug_testerat_login_process():
    """Debug the exact steps testerat takes during login"""
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        
        print("🔍 DEBUGGING TESTERAT LOGIN PROCESS")
        print("=" * 50)
        
        # Step 1: Navigate to app
        print("1️⃣ Navigating to http://localhost:3002")
        page.goto("http://localhost:3002")
        page.wait_for_load_state('networkidle')
        print(f"   Current URL: {page.url}")
        
        # Step 2: Check initial authentication state
        print("\n2️⃣ Checking initial authentication state...")
        sign_out_btn = page.query_selector("button:has-text('Sign Out')")
        profile_link = page.query_selector("a:has-text('Profile')")
        log_in_btn = page.query_selector("button:has-text('Log In')")
        
        print(f"   Sign Out button found: {bool(sign_out_btn)}")
        print(f"   Profile link found: {bool(profile_link)}")
        print(f"   Log In button found: {bool(log_in_btn)}")
        
        if sign_out_btn or profile_link:
            print("   ✅ User appears to already be logged in!")
            print("   🔄 Logging out first...")
            if sign_out_btn:
                sign_out_btn.click()
                page.wait_for_load_state('networkidle')
                time.sleep(2)
        
        # Step 3: Navigate to login page (exactly like testerat does)
        print("\n3️⃣ Navigating to login page...")
        page.goto("http://localhost:3002/login")
        page.wait_for_load_state('networkidle')
        print(f"   Current URL: {page.url}")
        
        # Step 4: Find login form elements (exactly like testerat does)
        print("\n4️⃣ Finding login form elements...")
        email_input = page.query_selector("input[id='email']")
        password_input = page.query_selector("input[id='password']")
        submit_btn = page.query_selector("button[type='submit']")
        
        print(f"   Email input found: {bool(email_input)}")
        print(f"   Password input found: {bool(password_input)}")
        print(f"   Submit button found: {bool(submit_btn)}")
        
        if not email_input or not password_input or not submit_btn:
            print("   ❌ Login form elements not found!")
            browser.close()
            return
        
        # Step 5: Fill credentials (exactly like testerat does)
        print("\n5️⃣ Filling credentials...")
        print("   Email: <EMAIL>")
        print("   Password: testpassword")
        
        email_input.fill("<EMAIL>")
        password_input.fill("testpassword")
        
        # Step 6: Submit form (exactly like testerat does)
        print("\n6️⃣ Submitting form...")
        submit_btn.click()
        
        # Step 7: Wait and check what happens (exactly like testerat does)
        print("\n7️⃣ Waiting for response...")
        page.wait_for_load_state('networkidle')
        time.sleep(3)  # Same wait as testerat
        
        print(f"   Current URL after submit: {page.url}")
        
        # Step 8: Check for authentication indicators (exactly like testerat does)
        print("\n8️⃣ Checking for authentication indicators...")
        
        # This is exactly what testerat looks for:
        auth_indicator = page.query_selector("button:has-text('Sign Out')")
        print(f"   Auth indicator (Sign Out button): {bool(auth_indicator)}")
        
        if auth_indicator:
            print("   ✅ TESTERAT SHOULD DETECT LOGIN SUCCESS!")
        else:
            print("   ❌ TESTERAT WILL THINK LOGIN FAILED!")
            
            # Let's see what IS on the page
            print("\n🔍 What's actually on the page:")
            
            # Check all buttons
            buttons = page.query_selector_all("button")
            print(f"   Found {len(buttons)} buttons:")
            for i, btn in enumerate(buttons[:5]):
                text = btn.text_content() or ""
                if text.strip():
                    print(f"     Button {i+1}: '{text.strip()}'")
            
            # Check all links
            links = page.query_selector_all("a")
            print(f"   Found {len(links)} links:")
            for i, link in enumerate(links[:5]):
                text = link.text_content() or ""
                href = link.get_attribute("href") or ""
                if text.strip():
                    print(f"     Link {i+1}: '{text.strip()}' -> {href}")
            
            # Check for error messages
            error_selectors = [".error", "[role='alert']", ".text-red-500"]
            for selector in error_selectors:
                error_elem = page.query_selector(selector)
                if error_elem:
                    error_text = error_elem.text_content()
                    print(f"   Error found ({selector}): {error_text}")
        
        print("\n9️⃣ Keeping browser open for 10 seconds for manual inspection...")
        time.sleep(10)
        
        browser.close()

if __name__ == "__main__":
    debug_testerat_login_process()

#!/usr/bin/env python3
"""
Detailed Login Debug - Check for error messages and network requests
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent))

from playwright.sync_api import sync_playwright
import time

def debug_login_detailed():
    """Debug login with detailed error checking"""
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        
        # Listen to console messages
        console_messages = []
        page.on("console", lambda msg: console_messages.append(f"{msg.type}: {msg.text}"))
        
        # Listen to network requests
        network_requests = []
        page.on("request", lambda req: network_requests.append(f"REQUEST: {req.method} {req.url}"))
        page.on("response", lambda resp: network_requests.append(f"RESPONSE: {resp.status} {resp.url}"))
        
        print("🔍 Starting detailed login debug...")
        
        # Navigate to login page directly
        print("1️⃣ Going directly to login page...")
        page.goto("http://localhost:3002/login")
        page.wait_for_load_state('networkidle')
        time.sleep(2)
        
        # Fill credentials and submit
        print("2️⃣ Filling credentials...")
        email_input = page.query_selector("input[id='email']")
        password_input = page.query_selector("input[id='password']")
        submit_btn = page.query_selector("button[type='submit']")
        
        if email_input and password_input and submit_btn:
            email_input.fill("<EMAIL>")
            password_input.fill("testpassword")
            
            print("3️⃣ Submitting form...")
            submit_btn.click()
            
            # Wait and check for errors
            time.sleep(5)
            
            print("4️⃣ Checking for error messages...")
            
            # Check for various error selectors
            error_selectors = [
                ".error",
                "[role='alert']", 
                ".text-red-500",
                ".text-red-600",
                ".text-destructive",
                ".alert-error",
                ".error-message",
                "div:has-text('error')",
                "div:has-text('invalid')",
                "div:has-text('incorrect')",
                "div:has-text('failed')"
            ]
            
            for selector in error_selectors:
                error_elem = page.query_selector(selector)
                if error_elem:
                    error_text = error_elem.text_content()
                    if error_text and error_text.strip():
                        print(f"   ❌ Error found ({selector}): {error_text}")
            
            print("5️⃣ Console messages:")
            for msg in console_messages[-10:]:  # Last 10 messages
                print(f"   {msg}")
            
            print("6️⃣ Recent network requests:")
            for req in network_requests[-10:]:  # Last 10 requests
                print(f"   {req}")
            
            print(f"7️⃣ Current URL: {page.url}")
            
            # Check authentication state
            logout_btn = page.query_selector("button:has-text('Sign Out')")
            profile_link = page.query_selector("a:has-text('Profile')")
            
            if logout_btn or profile_link:
                print("✅ LOGIN SUCCESSFUL!")
            else:
                print("❌ LOGIN FAILED!")
                
                # Take screenshot
                page.screenshot(path="login_failed.png")
                print("   Screenshot saved as login_failed.png")
        
        time.sleep(5)
        browser.close()

if __name__ == "__main__":
    debug_login_detailed()

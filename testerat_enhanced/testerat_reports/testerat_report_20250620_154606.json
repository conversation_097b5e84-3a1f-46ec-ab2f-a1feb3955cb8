{"metadata": {"report_id": "20250620_154606", "generated_at": "2025-06-20T15:46:06.677833", "testerat_version": "2.0.0", "framework_info": {"framework": "FrameworkType.UNKNOWN", "app_name": "503 Service Temporarily Unavailable", "version": null, "features": [], "confidence": 0.1, "detection_methods": ["DOM analysis"], "detected_patterns": []}, "app_info": {"name": "503 Service Temporarily Unavailable", "url": "https://httpbin.org"}}, "summary": {"suite_name": "Enhanced Testerat - https://httpbin.org", "total_tests": 9, "passed": 5, "failed": 2, "critical_issues": 0, "success_rate": 55.55555555555556, "total_execution_time": 2.7004811763763428, "start_time": "2025-06-20T15:46:06.677937", "end_time": "2025-06-20T15:46:09.378416"}, "test_results": [{"test_name": "authentication_system_detection", "status": "SKIPPED", "details": "No authentication system detected on this site", "severity": "LOW", "recommendations": ["This appears to be a public site without user authentication", "If authentication should be present, verify login forms and links are properly implemented"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-20T15:46:08.163258", "framework_detected": "unknown", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "workflow_detection", "status": "SKIPPED", "details": "No multi-step workflow detected", "severity": "LOW", "recommendations": ["Add workflow testing if multi-step processes exist"], "execution_time": 0.0, "screenshot_path": null, "category": "workflow", "timestamp": "2025-06-20T15:46:08.189710", "framework_detected": "unknown", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "csrf_protection", "status": "FAILED", "details": "No CSRF token found", "severity": "MEDIUM", "recommendations": ["Implement CSRF protection for form submissions"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-20T15:46:08.200023", "framework_detected": "unknown", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_endpoints", "status": "FAILED", "details": "Server error on /api/auth/signin: 503; Server error on /api/auth/signout: 503; Server error on /api/csrf-token: 503; Server error on /api/user/profile: 503; Server error on /api/forms/submit: 503", "severity": "MEDIUM", "recommendations": ["Fix server error on /api/auth/signin", "Fix server error on /api/auth/signout", "Fix server error on /api/csrf-token", "Fix server error on /api/user/profile", "Fix server error on /api/forms/submit"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-20T15:46:09.315272", "framework_detected": "unknown", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_error_handling", "status": "PASSED", "details": "API error handling working properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-20T15:46:09.315359", "framework_detected": "unknown", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_security", "status": "PASSED", "details": "API security checks passed", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-20T15:46:09.315376", "framework_detected": "unknown", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_security", "status": "PASSED", "details": "Basic security checks passed", "severity": "LOW", "recommendations": ["Security basics look good - consider comprehensive security audit"], "execution_time": 0.0, "screenshot_path": null, "category": "security", "timestamp": "2025-06-20T15:46:09.374234", "framework_detected": "unknown", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_accessibility", "status": "PASSED", "details": "Basic accessibility checks passed", "severity": "LOW", "recommendations": ["Accessibility basics look good - consider comprehensive accessibility audit"], "execution_time": 0.0, "screenshot_path": null, "category": "accessibility", "timestamp": "2025-06-20T15:46:09.376633", "framework_detected": "unknown", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_performance", "status": "PASSED", "details": "Performance metrics within acceptable ranges", "severity": "LOW", "recommendations": ["Performance looks good - continue monitoring"], "execution_time": 0.0, "screenshot_path": null, "category": "performance", "timestamp": "2025-06-20T15:46:09.378393", "framework_detected": "unknown", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "critical_issues": [], "failures": [{"test_name": "csrf_protection", "status": "FAILED", "details": "No CSRF token found", "severity": "MEDIUM", "recommendations": ["Implement CSRF protection for form submissions"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-20T15:46:08.200023", "framework_detected": "unknown", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_endpoints", "status": "FAILED", "details": "Server error on /api/auth/signin: 503; Server error on /api/auth/signout: 503; Server error on /api/csrf-token: 503; Server error on /api/user/profile: 503; Server error on /api/forms/submit: 503", "severity": "MEDIUM", "recommendations": ["Fix server error on /api/auth/signin", "Fix server error on /api/auth/signout", "Fix server error on /api/csrf-token", "Fix server error on /api/user/profile", "Fix server error on /api/forms/submit"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-20T15:46:09.315272", "framework_detected": "unknown", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "recommendations": ["Fix server error on /api/auth/signin", "Fix server error on /api/forms/submit", "Add workflow testing if multi-step processes exist", "Implement CSRF protection for form submissions", "If authentication should be present, verify login forms and links are properly implemented", "Security basics look good - consider comprehensive security audit", "Accessibility basics look good - consider comprehensive accessibility audit", "Fix server error on /api/csrf-token", "Fix server error on /api/auth/signout", "Fix server error on /api/user/profile", "This appears to be a public site without user authentication", "Performance looks good - continue monitoring"], "fix_examples": [], "code_locations": [], "statistics": {"categories": {"general": {"total": 1, "passed": 1, "failed": 0}, "workflow": {"total": 1, "passed": 1, "failed": 0}, "api": {"total": 4, "passed": 2, "failed": 2}, "security": {"total": 1, "passed": 1, "failed": 0}, "accessibility": {"total": 1, "passed": 1, "failed": 0}, "performance": {"total": 1, "passed": 1, "failed": 0}}, "severities": {"LOW": 7, "MEDIUM": 2}, "avg_execution_time": 0.0, "slowest_test": "authentication_system_detection", "fastest_test": "authentication_system_detection"}}
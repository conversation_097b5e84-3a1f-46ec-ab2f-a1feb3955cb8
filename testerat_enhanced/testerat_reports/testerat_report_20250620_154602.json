{"metadata": {"report_id": "20250620_154602", "generated_at": "2025-06-20T15:46:02.404192", "testerat_version": "2.0.0", "framework_info": {"framework": "FrameworkType.UNKNOWN", "app_name": "Example Domain", "version": null, "features": [], "confidence": 0.1, "detection_methods": ["DOM analysis"], "detected_patterns": []}, "app_info": {"name": "Example Domain", "url": "https://example.com"}}, "summary": {"suite_name": "Enhanced Testerat - https://example.com", "total_tests": 9, "passed": 6, "failed": 1, "critical_issues": 0, "success_rate": 66.66666666666666, "total_execution_time": 4.2395689487457275, "start_time": "2025-06-20T15:46:02.404315", "end_time": "2025-06-20T15:46:06.643879"}, "test_results": [{"test_name": "authentication_system_detection", "status": "SKIPPED", "details": "No authentication system detected on this site", "severity": "LOW", "recommendations": ["This appears to be a public site without user authentication", "If authentication should be present, verify login forms and links are properly implemented"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-20T15:46:04.315581", "framework_detected": "unknown", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "workflow_detection", "status": "SKIPPED", "details": "No multi-step workflow detected", "severity": "LOW", "recommendations": ["Add workflow testing if multi-step processes exist"], "execution_time": 0.0, "screenshot_path": null, "category": "workflow", "timestamp": "2025-06-20T15:46:04.341265", "framework_detected": "unknown", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "csrf_protection", "status": "FAILED", "details": "No CSRF token found", "severity": "MEDIUM", "recommendations": ["Implement CSRF protection for form submissions"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-20T15:46:04.355645", "framework_detected": "unknown", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_endpoints", "status": "PASSED", "details": "API endpoints working properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-20T15:46:06.564540", "framework_detected": "unknown", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_error_handling", "status": "PASSED", "details": "API error handling working properly", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-20T15:46:06.564653", "framework_detected": "unknown", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "api_security", "status": "PASSED", "details": "API security checks passed", "severity": "LOW", "recommendations": [], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-20T15:46:06.564672", "framework_detected": "unknown", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_security", "status": "PASSED", "details": "Basic security checks passed", "severity": "LOW", "recommendations": ["Security basics look good - consider comprehensive security audit"], "execution_time": 0.0, "screenshot_path": null, "category": "security", "timestamp": "2025-06-20T15:46:06.638860", "framework_detected": "unknown", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_accessibility", "status": "PASSED", "details": "Basic accessibility checks passed", "severity": "LOW", "recommendations": ["Accessibility basics look good - consider comprehensive accessibility audit"], "execution_time": 0.0, "screenshot_path": null, "category": "accessibility", "timestamp": "2025-06-20T15:46:06.642144", "framework_detected": "unknown", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_performance", "status": "PASSED", "details": "Performance metrics within acceptable ranges", "severity": "LOW", "recommendations": ["Performance looks good - continue monitoring"], "execution_time": 0.0, "screenshot_path": null, "category": "performance", "timestamp": "2025-06-20T15:46:06.643869", "framework_detected": "unknown", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "critical_issues": [], "failures": [{"test_name": "csrf_protection", "status": "FAILED", "details": "No CSRF token found", "severity": "MEDIUM", "recommendations": ["Implement CSRF protection for form submissions"], "execution_time": 0.0, "screenshot_path": null, "category": "api", "timestamp": "2025-06-20T15:46:04.355645", "framework_detected": "unknown", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "recommendations": ["Add workflow testing if multi-step processes exist", "Implement CSRF protection for form submissions", "If authentication should be present, verify login forms and links are properly implemented", "Security basics look good - consider comprehensive security audit", "Accessibility basics look good - consider comprehensive accessibility audit", "This appears to be a public site without user authentication", "Performance looks good - continue monitoring"], "fix_examples": [], "code_locations": [], "statistics": {"categories": {"general": {"total": 1, "passed": 1, "failed": 0}, "workflow": {"total": 1, "passed": 1, "failed": 0}, "api": {"total": 4, "passed": 3, "failed": 1}, "security": {"total": 1, "passed": 1, "failed": 0}, "accessibility": {"total": 1, "passed": 1, "failed": 0}, "performance": {"total": 1, "passed": 1, "failed": 0}}, "severities": {"LOW": 8, "MEDIUM": 1}, "avg_execution_time": 0.0, "slowest_test": "authentication_system_detection", "fastest_test": "authentication_system_detection"}}
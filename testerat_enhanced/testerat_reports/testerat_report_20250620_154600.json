{"metadata": {"report_id": "20250620_154600", "generated_at": "2025-06-20T15:46:00.280035", "testerat_version": "2.0.0", "framework_info": {"framework": "FrameworkType.UNKNOWN", "app_name": "503 Service Temporarily Unavailable", "version": null, "features": [], "confidence": 0.1, "detection_methods": ["DOM analysis"], "detected_patterns": []}, "app_info": {"name": "503 Service Temporarily Unavailable", "url": "https://httpbin.org"}}, "summary": {"suite_name": "Enhanced Testerat - https://httpbin.org", "total_tests": 4, "passed": 3, "failed": 0, "critical_issues": 0, "success_rate": 75.0, "total_execution_time": 1.5561060905456543, "start_time": "2025-06-20T15:46:00.280180", "end_time": "2025-06-20T15:46:01.836280"}, "test_results": [{"test_name": "authentication_system_detection", "status": "SKIPPED", "details": "No authentication system detected on this site", "severity": "LOW", "recommendations": ["This appears to be a public site without user authentication", "If authentication should be present, verify login forms and links are properly implemented"], "execution_time": 0.0, "screenshot_path": null, "category": "general", "timestamp": "2025-06-20T15:46:01.768223", "framework_detected": "unknown", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_security", "status": "PASSED", "details": "Basic security checks passed", "severity": "LOW", "recommendations": ["Security basics look good - consider comprehensive security audit"], "execution_time": 0.0, "screenshot_path": null, "category": "security", "timestamp": "2025-06-20T15:46:01.830592", "framework_detected": "unknown", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_accessibility", "status": "PASSED", "details": "Basic accessibility checks passed", "severity": "LOW", "recommendations": ["Accessibility basics look good - consider comprehensive accessibility audit"], "execution_time": 0.0, "screenshot_path": null, "category": "accessibility", "timestamp": "2025-06-20T15:46:01.834812", "framework_detected": "unknown", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}, {"test_name": "basic_performance", "status": "PASSED", "details": "Performance metrics within acceptable ranges", "severity": "LOW", "recommendations": ["Performance looks good - continue monitoring"], "execution_time": 0.0, "screenshot_path": null, "category": "performance", "timestamp": "2025-06-20T15:46:01.836273", "framework_detected": "unknown", "auth_method_detected": null, "error_details": null, "code_location": null, "fix_examples": [], "performance_metrics": {}, "security_issues": [], "vulnerability_type": null}], "critical_issues": [], "failures": [], "recommendations": ["If authentication should be present, verify login forms and links are properly implemented", "Security basics look good - consider comprehensive security audit", "Accessibility basics look good - consider comprehensive accessibility audit", "This appears to be a public site without user authentication", "Performance looks good - continue monitoring"], "fix_examples": [], "code_locations": [], "statistics": {"categories": {"general": {"total": 1, "passed": 1, "failed": 0}, "security": {"total": 1, "passed": 1, "failed": 0}, "accessibility": {"total": 1, "passed": 1, "failed": 0}, "performance": {"total": 1, "passed": 1, "failed": 0}}, "severities": {"LOW": 4}, "avg_execution_time": 0.0, "slowest_test": "authentication_system_detection", "fastest_test": "authentication_system_detection"}}
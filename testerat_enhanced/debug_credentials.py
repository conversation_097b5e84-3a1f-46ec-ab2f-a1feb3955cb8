#!/usr/bin/env python3
"""
Debug exactly what credentials testerat is using
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent))

from playwright.sync_api import sync_playwright
from testerat_enhanced import UniversalTestConfig

def debug_credentials():
    """Debug the exact credentials being used"""
    
    print("🔍 DEBUGGING TESTERAT CREDENTIALS")
    print("=" * 50)
    
    # Check config credentials
    config = UniversalTestConfig()
    
    print("1️⃣ Configuration credentials:")
    print(f"   Email: '{config.auth_config.test_users['standard']['email']}'")
    print(f"   Password: '{config.auth_config.test_users['standard']['password']}'")
    
    # Update config like testerat does
    config.auth_config.selectors.update({
        "auth_indicator": "button:has-text('Sign Out')",
        "logout_button": "button:has-text('Sign Out')",
        "user_menu": "a:has-text('Profile')",
        "email_input": "input[id='email']",
        "password_input": "input[id='password']",
        "login_button": "button[type='submit']"
    })
    
    config.auth_config.login_url = "/login"
    
    print("\n2️⃣ Updated configuration:")
    print(f"   Email: '{config.auth_config.test_users['standard']['email']}'")
    print(f"   Password: '{config.auth_config.test_users['standard']['password']}'")
    print(f"   Login URL: '{config.auth_config.login_url}'")
    
    # Test actual form filling
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        
        print("\n3️⃣ Testing actual form filling...")
        page.goto("http://localhost:3001/login")
        page.wait_for_load_state('networkidle')
        
        # Find form elements
        email_input = page.query_selector("input[id='email']")
        password_input = page.query_selector("input[id='password']")
        
        if email_input and password_input:
            # Get test credentials
            test_user = config.auth_config.test_users["standard"]
            
            print(f"   About to fill email: '{test_user['email']}'")
            print(f"   About to fill password: '{test_user['password']}'")
            
            # Fill the form
            email_input.fill(test_user["email"])
            password_input.fill(test_user["password"])
            
            # Check what was actually filled
            filled_email = email_input.input_value()
            filled_password = password_input.input_value()
            
            print(f"   Actually filled email: '{filled_email}'")
            print(f"   Actually filled password: '{filled_password}'")
            
            if filled_email == test_user["email"] and filled_password == test_user["password"]:
                print("   ✅ Credentials filled correctly!")
            else:
                print("   ❌ Credentials NOT filled correctly!")
                
        else:
            print("   ❌ Form elements not found!")
            print(f"   Email input found: {bool(email_input)}")
            print(f"   Password input found: {bool(password_input)}")
        
        print("\n4️⃣ Keeping browser open for 10 seconds for manual inspection...")
        page.wait_for_timeout(10000)
        
        browser.close()

if __name__ == "__main__":
    debug_credentials()

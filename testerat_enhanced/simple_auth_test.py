#!/usr/bin/env python3
"""
Simple authentication test to verify login is working
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent))

from testerat_enhanced.engines.authentication import AuthenticationEngine
from testerat_enhanced.config.test_config import UniversalTestConfig
from playwright.sync_api import sync_playwright

def test_simple_auth():
    """Simple test to verify authentication is working"""
    
    print("🔍 Starting simple authentication test...")
    
    config = UniversalTestConfig()
    
    # Update config for FAAFO app
    config.auth_config.selectors.update({
        "auth_indicator": "button:has-text('Sign Out')",
        "logout_button": "button:has-text('Sign Out')",
        "user_menu": "a:has-text('Profile')",
        "email_input": "input[id='email']",
        "password_input": "input[id='password']",
        "login_button": "button[type='submit']"
    })
    
    config.auth_config.login_url = "/login"
    
    print(f"✅ Config updated")
    print(f"   - Auth indicator: {config.auth_config.selectors['auth_indicator']}")
    print(f"   - Test user: {config.auth_config.test_users['standard']['email']}")
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        
        # Navigate to app
        print(f"🌐 Navigating to http://localhost:3001")
        page.goto("http://localhost:3001")
        page.wait_for_load_state('networkidle')
        
        # Initialize auth engine
        auth_engine = AuthenticationEngine(config.auth_config)
        
        # Test authentication
        print(f"🔐 Testing authentication...")
        result = auth_engine.test_authentication_flow(page, "http://localhost:3002")
        
        print(f"📊 Authentication test result:")
        print(f"   - Status: {result.status}")
        print(f"   - Details: {result.details}")
        print(f"   - Authenticated: {auth_engine.current_auth_state.is_authenticated}")
        
        if result.status == "PASSED":
            print("✅ AUTHENTICATION WORKING!")
        else:
            print("❌ AUTHENTICATION FAILED!")
            print(f"   - Recommendations: {result.recommendations}")
        
        # Keep browser open for inspection
        print("🔍 Keeping browser open for 10 seconds...")
        page.wait_for_timeout(10000)
        
        browser.close()

if __name__ == "__main__":
    test_simple_auth()

#!/usr/bin/env python3
"""
Debug Login Process - Check exactly what happens during login
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent))

from playwright.sync_api import sync_playwright
import time

def debug_login():
    """Debug the exact login process step by step"""
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        
        print("🔍 Starting login debug...")
        
        # Step 1: Navigate to the app
        print("1️⃣ Navigating to http://localhost:3002")
        page.goto("http://localhost:3002")
        page.wait_for_load_state('networkidle')
        
        # Step 2: Check initial state
        print("2️⃣ Checking initial page state...")
        title = page.title()
        print(f"   Page title: {title}")
        
        # Check for login/logout buttons
        login_btn = page.query_selector("button:has-text('Log In')")
        logout_btn = page.query_selector("button:has-text('Sign Out')")
        profile_link = page.query_selector("a:has-text('Profile')")
        
        print(f"   Login button found: {bool(login_btn)}")
        print(f"   Logout button found: {bool(logout_btn)}")
        print(f"   Profile link found: {bool(profile_link)}")
        
        if logout_btn or profile_link:
            print("✅ User appears to already be logged in!")
            browser.close()
            return
        
        # Step 3: Navigate to login page
        print("3️⃣ Navigating to login page...")
        if login_btn:
            print("   Clicking Log In button")
            login_btn.click()
        else:
            print("   Going directly to /login")
            page.goto("http://localhost:3002/login")
        
        page.wait_for_load_state('networkidle')
        time.sleep(2)
        
        # Step 4: Check login form
        print("4️⃣ Checking login form...")
        email_input = page.query_selector("input[id='email']")
        password_input = page.query_selector("input[id='password']")
        submit_btn = page.query_selector("button[type='submit']")
        
        print(f"   Email input found: {bool(email_input)}")
        print(f"   Password input found: {bool(password_input)}")
        print(f"   Submit button found: {bool(submit_btn)}")
        
        if not email_input or not password_input or not submit_btn:
            print("❌ Login form elements not found!")
            browser.close()
            return
        
        # Step 5: Fill in credentials
        print("5️⃣ Filling in credentials...")
        print("   Email: <EMAIL>")
        print("   Password: testpassword")
        
        email_input.fill("<EMAIL>")
        password_input.fill("testpassword")
        
        # Step 6: Submit form
        print("6️⃣ Submitting form...")
        submit_btn.click()
        
        # Wait for response
        print("   Waiting for response...")
        page.wait_for_load_state('networkidle')
        time.sleep(3)
        
        # Step 7: Check result
        print("7️⃣ Checking login result...")
        current_url = page.url
        print(f"   Current URL: {current_url}")
        
        # Check for authentication indicators
        logout_btn = page.query_selector("button:has-text('Sign Out')")
        profile_link = page.query_selector("a:has-text('Profile')")
        error_msg = page.query_selector(".error, [role='alert'], .text-red-500")
        
        print(f"   Logout button found: {bool(logout_btn)}")
        print(f"   Profile link found: {bool(profile_link)}")
        print(f"   Error message found: {bool(error_msg)}")
        
        if error_msg:
            error_text = error_msg.text_content()
            print(f"   Error message: {error_text}")
        
        if logout_btn or profile_link:
            print("✅ LOGIN SUCCESSFUL!")
        else:
            print("❌ LOGIN FAILED!")
            
            # Check if still on login page
            if "/login" in current_url:
                print("   Still on login page - credentials might be wrong")
            
            # Take a screenshot for debugging
            page.screenshot(path="login_debug.png")
            print("   Screenshot saved as login_debug.png")
        
        print("8️⃣ Keeping browser open for 10 seconds for manual inspection...")
        time.sleep(10)
        
        browser.close()

if __name__ == "__main__":
    debug_login()
